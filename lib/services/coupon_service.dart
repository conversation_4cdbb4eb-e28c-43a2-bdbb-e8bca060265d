import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class CouponService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Kupon bilgilerini getir
  static Future<Map<String, dynamic>?> getCouponInfo(String couponCode) async {
    try {
      var couponDoc = await _firestore
          .collection('settings')
          .doc('firstOrderCoupon')
          .get();

      if (!couponDoc.exists) {
        return null;
      }

      var couponData = couponDoc.data()!;
      
      // Kupon kodu eşleşiyor mu?
      if (couponData['code'] != couponCode) {
        return null;
      }

      // Kupon aktif mi?
      if (!(couponData['isActive'] ?? false)) {
        return null;
      }

      return couponData;
    } catch (e) {
      print('Kupon bilgisi alınırken hata: $e');
      return null;
    }
  }

  // Kupon geçerliliğini kontrol et
  static Future<CouponValidationResult> validateCoupon({
    required String couponCode,
    required double orderTotal,
    required bool isFirstOrder,
  }) async {
    try {
      // İlk sipariş kontrolü
      if (!isFirstOrder) {
        return CouponValidationResult(
          isValid: false,
          message: 'Bu kupon sadece ilk siparişlerde geçerlidir.',
        );
      }

      // Kupon bilgilerini getir
      var couponData = await getCouponInfo(couponCode);
      if (couponData == null) {
        return CouponValidationResult(
          isValid: false,
          message: 'Geçersiz kupon kodu.',
        );
      }

      // Minimum sipariş tutarı kontrolü
      double minOrderAmount = (couponData['minOrderAmount'] ?? 0).toDouble();
      if (orderTotal < minOrderAmount) {
        return CouponValidationResult(
          isValid: false,
          message: 'Minimum sipariş tutarı ${minOrderAmount.toStringAsFixed(0)} TL olmalıdır.',
        );
      }

      // Kullanıcının daha önce bu kuponu kullanıp kullanmadığını kontrol et
      String userId = _auth.currentUser!.uid;
      List<dynamic> usedBy = couponData['usedBy'] ?? [];
      if (usedBy.contains(userId)) {
        return CouponValidationResult(
          isValid: false,
          message: 'Bu kuponu daha önce kullandınız.',
        );
      }

      // Kupon geçerli
      return CouponValidationResult(
        isValid: true,
        message: 'Kupon başarıyla uygulandı!',
        discountAmount: (couponData['discountAmount'] ?? 0).toDouble(),
        couponData: couponData,
      );
    } catch (e) {
      print('Kupon doğrulama hatası: $e');
      return CouponValidationResult(
        isValid: false,
        message: 'Kupon doğrulanırken bir hata oluştu.',
      );
    }
  }

  // Kupon kullanımını kaydet
  static Future<bool> recordCouponUsage(String couponCode) async {
    try {
      String userId = _auth.currentUser!.uid;
      
      await _firestore.collection('settings').doc('firstOrderCoupon').update({
        'currentUsage': FieldValue.increment(1),
        'usedBy': FieldValue.arrayUnion([userId]),
      });

      return true;
    } catch (e) {
      print('Kupon kullanım kaydı hatası: $e');
      return false;
    }
  }

  // Kupon indirimini hesapla
  static double calculateDiscount(double orderTotal, double discountAmount) {
    // İndirim tutarı sipariş toplamından fazla olamaz
    return discountAmount > orderTotal ? orderTotal : discountAmount;
  }
}

class CouponValidationResult {
  final bool isValid;
  final String message;
  final double discountAmount;
  final Map<String, dynamic>? couponData;

  CouponValidationResult({
    required this.isValid,
    required this.message,
    this.discountAmount = 0.0,
    this.couponData,
  });
}

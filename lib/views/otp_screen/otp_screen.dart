import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:pinput/pinput.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/font_config.dart';
import '../../config/font_family.dart';
import '../../config/image_path.dart';
import '../../config/size_config.dart';
import '../../config/string_config.dart';
import 'package:yemekkapimda/controller/dark_mode_controller.dart';
import 'package:yemekkapimda/utils/common_material_button.dart';
import '../../utils/appbar_common.dart';

class OtpScreen extends StatelessWidget {
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final TextEditingController otpController = TextEditingController();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  OtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final String? verificationId = Get.arguments?['verificationId'];
    final String? firstName = Get.arguments?['firstName'];
    final String? lastName = Get.arguments?['lastName'];
    final bool isReauthentication =
        Get.arguments?['isReauthentication'] ?? false;

    if (verificationId == null) {
      Get.snackbar(
        'Hata',
        'Doğrulama kodu alınamadı',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return const Scaffold(
        body: Center(
          child: Text('Doğrulama kodu alınamadı.'),
        ),
      );
    }

    return Obx(() => Scaffold(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(SizeConfig.kHeight100),
            child: CommonAppBar(
              title: "",
              leadingImage: ImagePath.arrow,
              color: darkModeController.isLightTheme.value
                  ? ColorConfig.kBlackColor
                  : ColorConfig.kWhiteColor,
              leadingOnTap: () => Get.back(),
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Center(
                  child: Text(
                    StringConfig.otpVerification,
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistSemiBold,
                      fontSize: FontConfig.kFontSize24,
                      fontWeight: FontWeight.w600,
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kBlackColor
                          : ColorConfig.kWhiteColor,
                    ),
                  ),
                ),
                const SizedBox(height: SizeConfig.kHeight8),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: SizeConfig.kHeight20),
                  child: Text(
                    StringConfig.sendTheCode,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: FontFamilyConfig.urbanistSemiBold,
                      fontSize: FontConfig.kFontSize14,
                      fontWeight: FontWeight.w400,
                      color: darkModeController.isLightTheme.value
                          ? ColorConfig.kHintColor
                          : ColorConfig.kDarkModeDividerColor,
                    ),
                  ),
                ),
                const SizedBox(height: SizeConfig.kHeight40),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: SizeConfig.kHeight40),
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: Pinput(
                      controller: otpController,
                      length: 6,
                      keyboardType: TextInputType.number,
                      pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                      showCursor: true,
                      enableIMEPersonalizedLearning: false,
                      hapticFeedbackType: HapticFeedbackType.disabled,
                      autofillHints: const [],
                      useNativeKeyboard: true,
                      toolbarEnabled: false,
                      obscureText: false,
                      forceErrorState: false,
                      enableSuggestions: false,
                      textInputAction: TextInputAction.done,
                      closeKeyboardWhenCompleted: true,
                      contextMenuBuilder: (context, editableTextState) {
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                ),
                const SizedBox(height: SizeConfig.kHeight60),
                CommonMaterialButton(
                  height: SizeConfig.kHeight54,
                  buttonColor: ColorConfig.kPrimaryColor,
                  width: double.infinity,
                  buttonText: StringConfig.verify,
                  txtColor: ColorConfig.kWhiteColor,
                  onButtonClick: () => _verifyOTP(
                    context,
                    verificationId,
                    firstName,
                    lastName,
                    isReauthentication,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Future<void> _verifyOTP(
    BuildContext context,
    String verificationId,
    String? firstName,
    String? lastName,
    bool isReauthentication,
  ) async {
    try {
      final String otp = otpController.text.trim();
      
      if (otp.isEmpty || otp.length < 6) {
        Get.snackbar(
          "Hata",
          "Lütfen geçerli bir doğrulama kodu girin",
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      await _signInOrRegisterUser(credential, firstName, lastName);
    } catch (e) {
      Get.back();
      Get.snackbar(
        "Doğrulama Hatası",
        "Girilen kod hatalı veya süresi dolmuş.",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }


  Future<void> _signInOrRegisterUser(
      PhoneAuthCredential credential, String? firstName, String? lastName) async {
    try {
      print('🔥 Auth: Kullanıcı giriş/kayıt işlemi başlıyor...');
      UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        print('🔥 Auth: Kullanıcı başarıyla doğrulandı - UID: ${userCredential.user!.uid}');

        final userDoc =
            await _firestore.collection('users').doc(userCredential.user!.uid).get();

        print('🔥 Firestore: Kullanıcı dokümanı kontrol ediliyor...');

        if (userDoc.exists) {
          print('🔥 Firestore: Kullanıcı zaten mevcut, giriş yapılıyor');
          await Future.delayed(const Duration(milliseconds: 500));
          Get.back();
          await Get.offAllNamed(AppRoutes.bottomBar);
        } else if (firstName != null && lastName != null) {
          print('🔥 Firestore: Yeni kullanıcı, kayıt işlemi başlıyor');
          print('🔥 Firestore: firstName: $firstName, lastName: $lastName');
          await _saveUserToFirestore(userCredential.user!, firstName, lastName);
        } else {
          print('🔥 HATA: Kullanıcı bilgileri eksik - firstName: $firstName, lastName: $lastName');
          Get.back();
          Get.snackbar(
            'Hata',
            'Kullanıcı kayıtlı değil ve kullanıcı bilgileri eksik.',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        print('🔥 HATA: UserCredential.user null');
      }
    } catch (e) {
      print('🔥 HATA: _signInOrRegisterUser fonksiyonunda hata: $e');
      Get.back();
      rethrow;
    }
  }

  Future<void> _saveUserToFirestore(
      User user, String firstName, String lastName) async {
    try {
      print('🔥 Firestore: Kullanıcı kaydediliyor - UID: ${user.uid}');
      print('🔥 Firestore: firstName: $firstName, lastName: $lastName');
      print('🔥 Firestore: phoneNumber: ${user.phoneNumber}');

      await _firestore.collection('users').doc(user.uid).set({
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': user.phoneNumber,
        'role': 'user',
        'createdAt': FieldValue.serverTimestamp(),
      });

      print('🔥 Firestore: Kullanıcı başarıyla kaydedildi!');

      await Get.offAllNamed(AppRoutes.bottomBar);
      await Future.delayed(const Duration(milliseconds: 300));

      Get.snackbar(
        "Başarılı",
        "Hesabınız başarıyla oluşturuldu.",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      print('🔥 Firestore HATA: Kullanıcı kaydedilirken hata oluştu: $e');
      Get.snackbar(
        "Hata",
        "Kullanıcı kaydedilirken bir hata oluştu: $e",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../app_routes/app_routes.dart';
import '../../config/color.dart';
import '../../config/size_config.dart';
import '../../controller/dark_mode_controller.dart';
import '../../controller/home_controller.dart';
import '../../controller/setting_controller.dart';
import '../../controller/slider_controller.dart';
import 'widgets/app_bar_section.dart';
import 'widgets/search_section.dart';
import 'widgets/slider_section.dart';
import 'widgets/categories_section.dart';
import 'widgets/restaurants_section.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final HomeController homeController = Get.put(HomeController());
  final DarkModeController darkModeController = Get.put(DarkModeController());
  final SettingController settingController = Get.put(SettingController());
  final SliderController sliderController = Get.put(SliderController());
  final PageController pageController = PageController();

  // Favorileri tutan RxList
  final RxList<String> favoriteList = RxList<String>();

  // Kategorileri tutan Map
  final Map<String, Map<String, dynamic>> categoriesMap = {};

  // Firebase'den firms verisini çekmek için fonksiyon
  Future<List<Map>> getFirmsData() async {
    QuerySnapshot snapshot =
        await FirebaseFirestore.instance.collection('firms').get();
    List<Map> firms = snapshot.docs.map((doc) {
      var data = doc.data() as Map<String, dynamic>?;
      if (data != null) {
        data['id'] = doc.id;
        return data;
      } else {
        return {};
      }
    }).toList();
    firms.shuffle();
    return firms;
  }

  // Kategorileri çeken fonksiyon
  Future<void> getCategories() async {
    QuerySnapshot snapshot =
        await FirebaseFirestore.instance.collection('categories').get();

    List<QueryDocumentSnapshot> docs = snapshot.docs;
    List<String> selectedCategoryNames = [
      'BURGER',
      'ÇORBA',
      'DÖNER',
      'ETLİEKMEK',
      'KASAP',
      'LAHMACUN',
      'PİZZA',
      'TATLI',
    ];

    docs = docs.where((doc) {
      var data = doc.data() as Map<String, dynamic>?;
      return selectedCategoryNames.contains(data?['name']);
    }).toList();

    for (String categoryName in selectedCategoryNames) {
      for (var doc in docs) {
        var data = doc.data() as Map<String, dynamic>?;
        if (data?['name'] == categoryName) {
          categoriesMap[doc.id] = {
            'name': data?['name'] ?? 'Kategori Bilinmiyor',
            'imageUrl':
                data?['imageUrl'] ?? 'assets/images/category_placeholder.png'
          };
          break;
        }
      }
    }
  }

  // Ürünleri kontrol eden fonksiyon
  Future<bool> hasProducts(String firmId) async {
    QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('products')
        .where('firmId', isEqualTo: firmId)
        .get();
    return snapshot.docs.isNotEmpty;
  }

  // Favorileri toggle yapan fonksiyon
  Future<void> toggleFavorite(String firmId) async {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      Get.toNamed(AppRoutes.signInScreen);
      return;
    }

    final userRef =
        FirebaseFirestore.instance.collection('users').doc(user.uid);
    final userDoc = await userRef.get();

    if (!userDoc.exists ||
        !(userDoc.data()?.containsKey('favorites') ?? false)) {
      await userRef.set({'favorites': []}, SetOptions(merge: true));
    }

    final favorites = List<String>.from(userDoc.data()?['favorites'] ?? []);

    if (favorites.contains(firmId)) {
      favorites.remove(firmId);
    } else {
      favorites.add(firmId);
    }

    await userRef.update({'favorites': favorites});
    favoriteList.value = favorites;
    favoriteList.refresh();
  }

  // Favorileri getiren fonksiyon
  Future<List<String>> getFavorites() async {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return [];
    }

    final userRef =
        FirebaseFirestore.instance.collection('users').doc(user.uid);
    final userDoc = await userRef.get();

    if (!userDoc.exists) {
      return [];
    }

    return List<String>.from(userDoc.data()?['favorites'] ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Obx(
        () => Scaffold(
          backgroundColor: darkModeController.isLightTheme.value
              ? ColorConfig.kWhiteColor
              : ColorConfig.kBlackColor,
          resizeToAvoidBottomInset: false,
          body: FutureBuilder(
            future: Future.wait([getCategories(), getFavorites()]),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return const Center(child: Text("Bir hata oluştu."));
              }

              favoriteList.value = snapshot.data?[1] as List<String>? ?? [];

              return RefreshIndicator(
                color: ColorConfig.kPrimaryColor,
                onRefresh: () async {
                  // Yenileme işlemleri
                  await Future.wait([
                    getCategories(),
                    getFavorites(),
                    getFirmsData(),
                  ]);
                  setState(() {});
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: SizeConfig.kHeight26),
                      AppBarSection(darkModeController: darkModeController),
                      const SizedBox(height: SizeConfig.kHeight30),
                      SearchSection(darkModeController: darkModeController),
                      const SizedBox(height: SizeConfig.kHeight30),
                      SliderSection(
                        sliderController: sliderController,
                        pageController: pageController,
                      ),
                      const SizedBox(height: SizeConfig.kHeight30),
                      CategoriesSection(
                        darkModeController: darkModeController,
                        categoriesMap: categoriesMap,
                      ),
                      const SizedBox(height: SizeConfig.kHeight20),
                      RestaurantsSection(
                        darkModeController: darkModeController,
                        favoriteList: favoriteList,
                        onToggleFavorite: toggleFavorite,
                        getFirmsData: getFirmsData,
                        hasProducts: hasProducts,
                      ),
                      const SizedBox(height: SizeConfig.kHeight20),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

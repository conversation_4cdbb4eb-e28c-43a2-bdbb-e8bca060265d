import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:yemekkapimda/views/app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'controller/storage_controller.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:get/get.dart';
import 'services/event_bus_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Firebase başlatma işlemini güvenli şekilde yap
  await _initializeFirebase();

  String? countryCode = await StorageController.instance.getCountryCode();
  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

  Get.put(EventBusService());
  runApp(YemekKapimdaApp(
    countryCode: countryCode,
  ));
}

Future<void> _initializeFirebase() async {
  try {
    // Önce mevcut Firebase apps'leri kontrol et
    if (Firebase.apps.isNotEmpty) {
      print('🔥 Firebase zaten başlatılmış (${Firebase.apps.length} app mevcut)');
      return;
    }

    // Firebase'i başlat
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('🔥 Firebase başarıyla başlatıldı');

  } catch (e) {
    // Duplicate app hatası durumunda
    if (e.toString().contains('duplicate-app') ||
        e.toString().contains('[DEFAULT]') ||
        e.toString().contains('already exists')) {
      print('🔥 Firebase duplicate app hatası yakalandı, devam ediliyor...');
      return;
    }

    // Diğer hatalar için
    print('🔥 Firebase başlatma hatası: $e');

    // Firebase zaten başlatılmış olabilir, kontrol et
    try {
      Firebase.app(); // Default app'i kontrol et
      print('🔥 Firebase default app mevcut, devam ediliyor...');
      return;
    } catch (appError) {
      print('🔥 Firebase default app bulunamadı: $appError');
      rethrow; // Gerçek bir hata varsa fırlat
    }
  }
}

Future<void> checkForUpdate() async {
  final updateInfo = await InAppUpdate.checkForUpdate();
  if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
    await InAppUpdate.performImmediateUpdate();
  }
}

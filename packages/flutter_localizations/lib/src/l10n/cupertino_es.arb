{"datePickerHourSemanticsLabelOne": "$hour en punto", "datePickerHourSemanticsLabelOther": "$hour en punto", "datePickerMinuteSemanticsLabelOne": "1 minuto", "datePickerMinuteSemanticsLabelOther": "$minute minutos", "datePickerDateOrder": "dmy", "datePickerDateTimeOrder": "date_time_dayPeriod", "anteMeridiemAbbreviation": "a. m.", "postMeridiemAbbreviation": "p. m.", "todayLabel": "Hoy", "alertDialogLabel": "<PERSON><PERSON><PERSON>", "timerPickerHourLabelOne": "hora", "timerPickerHourLabelOther": "horas", "timerPickerMinuteLabelOne": "min", "timerPickerMinuteLabelOther": "min", "timerPickerSecondLabelOne": "s", "timerPickerSecondLabelOther": "s", "cutButtonLabel": "Cortar", "copyButtonLabel": "Copiar", "pasteButtonLabel": "<PERSON><PERSON><PERSON>", "selectAllButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "tabSemanticsLabel": "Pestaña $tabIndex de $tabCount", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON>", "searchTextFieldPlaceholderLabel": "Buscar", "noSpellCheckReplacementsLabel": "No se ha encontrado ninguna sustitución", "menuDismissLabel": "<PERSON><PERSON><PERSON>", "lookUpButtonLabel": "Buscador visual", "searchWebButtonLabel": "Buscar en la Web", "shareButtonLabel": "Compartir...", "clearButtonLabel": "Bo<PERSON>r", "cancelButtonLabel": "<PERSON><PERSON><PERSON>", "backButtonLabel": "Atrás"}
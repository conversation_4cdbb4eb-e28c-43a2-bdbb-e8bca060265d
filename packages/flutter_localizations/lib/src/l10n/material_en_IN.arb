{"clearButtonTooltip": "Clear text", "selectedDateLabel": "Selected", "searchWebButtonLabel": "Search Web", "shareButtonLabel": "Share", "scanTextButtonLabel": "Scan text", "lookUpButtonLabel": "Look up", "menuDismissLabel": "Dismiss menu", "expansionTileExpandedHint": "double-tap to collapse", "expansionTileCollapsedHint": "double-tap to expand", "expansionTileExpandedTapHint": "Collapse", "expansionTileCollapsedTapHint": "Expand for more details", "expandedHint": "Collapsed", "collapsedHint": "Expanded", "scrimLabel": "Scrim", "bottomSheetLabel": "Bottom sheet", "scrimOnTapHint": "Close $modalRouteContentName", "currentDateLabel": "Today", "keyboardKeyShift": "Shift", "menuBarMenuLabel": "Menu bar menu", "keyboardKeyNumpad8": "Num 8", "keyboardKeyCapsLock": "Caps lock", "keyboardKeyAltGraph": "AltGr", "keyboardKeyNumpad4": "Num 4", "keyboardKeyNumpad5": "Num 5", "keyboardKeyNumpad6": "Num 6", "keyboardKeyNumpad7": "Num 7", "keyboardKeyNumpad2": "Num 2", "keyboardKeyNumpad0": "Num 0", "keyboardKeyNumpadAdd": "Num +", "keyboardKeyNumpadComma": "<PERSON><PERSON> ,", "keyboardKeyNumpadDecimal": "Num .", "keyboardKeyNumpadDivide": "Num /", "keyboardKeyNumpadEqual": "Num =", "keyboardKeyNumpadMultiply": "Num *", "keyboardKeyNumpadParenLeft": "Num (", "keyboardKeyInsert": "Insert", "keyboardKeyHome": "Home", "keyboardKeyEject": "Eject", "keyboardKeyNumpadParenRight": "Num )", "keyboardKeyFn": "Fn", "keyboardKeyEscape": "Esc", "keyboardKeyEnd": "End", "keyboardKeyDelete": "Del", "keyboardKeyControl": "Ctrl", "keyboardKeyChannelUp": "Channel up", "keyboardKeyNumpadSubtract": "Num -", "keyboardKeyChannelDown": "Channel down", "keyboardKeyBackspace": "Backspace", "keyboardKeySelect": "Select", "keyboardKeyAlt": "Alt", "keyboardKeyMeta": "Meta", "keyboardKeyMetaMacOs": "Command", "keyboardKeyMetaWindows": "Win", "keyboardKeyNumLock": "Num lock", "keyboardKeyNumpad1": "Num 1", "keyboardKeyPageDown": "PgDown", "keyboardKeySpace": "Space", "keyboardKeyNumpad3": "Num 3", "keyboardKeyScrollLock": "Scroll lock", "keyboardKeyNumpad9": "Num 9", "keyboardKeyPrintScreen": "Print screen", "keyboardKeyPowerOff": "Power off", "keyboardKeyPower": "Power", "keyboardKeyPageUp": "PgUp", "keyboardKeyNumpadEnter": "Num enter", "inputTimeModeButtonLabel": "Switch to text input mode", "timePickerDialHelpText": "Select time", "timePickerHourLabel": "Hour", "timePickerMinuteLabel": "Minute", "invalidTimeLabel": "Enter a valid time", "dialModeButtonLabel": "Switch to dial picker mode", "timePickerInputHelpText": "Enter time", "dateSeparator": "/", "dateInputLabel": "Enter date", "calendarModeButtonLabel": "Switch to calendar", "dateRangePickerHelpText": "Select range", "datePickerHelpText": "Select date", "saveButtonLabel": "Save", "dateOutOfRangeLabel": "Out of range.", "invalidDateRangeLabel": "Invalid range.", "invalidDateFormatLabel": "Invalid format.", "dateRangeEndDateSemanticLabel": "End date $fullDate", "dateRangeStartDateSemanticLabel": "Start date $fullDate", "dateRangeEndLabel": "End date", "dateRangeStartLabel": "Start date", "inputDateModeButtonLabel": "Switch to input", "unspecifiedDateRange": "Date range", "unspecifiedDate": "Date", "selectYearSemanticsLabel": "Select year", "dateHelpText": "dd/mm/yyyy", "moreButtonTooltip": "More", "tabLabel": "Tab $tabIndex of $tabCount", "showAccountsLabel": "Show accounts", "hideAccountsLabel": "Hide accounts", "signedInLabel": "Signed in", "timePickerMinuteModeAnnouncement": "Select minutes", "timePickerHourModeAnnouncement": "Select hours", "scriptCategory": "English-like", "timeOfDayFormat": "h:mm a", "openAppDrawerTooltip": "Open navigation menu", "backButtonTooltip": "Back", "closeButtonTooltip": "Close", "deleteButtonTooltip": "Delete", "nextMonthTooltip": "Next month", "previousMonthTooltip": "Previous month", "nextPageTooltip": "Next page", "previousPageTooltip": "Previous page", "firstPageTooltip": "First page", "lastPageTooltip": "Last page", "showMenuTooltip": "Show menu", "aboutListTileTitle": "About $applicationName", "licensesPageTitle": "Licences", "licensesPackageDetailTextZero": "No licences", "licensesPackageDetailTextOne": "1 licence", "licensesPackageDetailTextOther": "$licenseCount licences", "pageRowsInfoTitle": "$firstRow–$lastRow of $rowCount", "pageRowsInfoTitleApproximate": "$firstRow–$lastRow of about $rowCount", "rowsPerPageTitle": "Rows per page:", "selectedRowCountTitleOne": "1 item selected", "selectedRowCountTitleOther": "$selectedRowCount items selected", "cancelButtonLabel": "Cancel", "closeButtonLabel": "Close", "continueButtonLabel": "Continue", "copyButtonLabel": "Copy", "cutButtonLabel": "Cut", "okButtonLabel": "OK", "pasteButtonLabel": "Paste", "selectAllButtonLabel": "Select all", "viewLicensesButtonLabel": "View licences", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON>", "drawerLabel": "Navigation menu", "popupMenuLabel": "Pop-up menu", "dialogLabel": "Dialogue", "alertDialogLabel": "<PERSON><PERSON>", "searchFieldLabel": "Search", "reorderItemToStart": "Move to the start", "reorderItemToEnd": "Move to the end", "reorderItemUp": "Move up", "reorderItemDown": "Move down", "reorderItemLeft": "Move to the left", "reorderItemRight": "Move to the right", "expandedIconTapHint": "Collapse", "collapsedIconTapHint": "Expand", "remainingTextFieldCharacterCountZero": "No characters remaining", "remainingTextFieldCharacterCountOne": "1 character remaining", "remainingTextFieldCharacterCountOther": "$remainingCount characters remaining", "refreshIndicatorSemanticLabel": "Refresh"}
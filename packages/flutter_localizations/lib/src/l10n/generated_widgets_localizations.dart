// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been automatically generated. Please do not edit it manually.
// To regenerate the file, use:
// dart dev/tools/localization/bin/gen_localizations.dart --overwrite

import 'dart:collection';
import 'dart:ui';

import '../widgets_localizations.dart';

// The classes defined here encode all of the translations found in the
// `flutter_localizations/lib/src/l10n/*.arb` files.
//
// These classes are constructed by the [getWidgetsTranslation] method at the
// bottom of this file, and used by the [_WidgetsLocalizationsDelegate.load]
// method defined in `flutter_localizations/lib/src/widgets_localizations.dart`.

// TODO(goderbauer): Extend the generator to properly format the output.
// dart format off

/// The translations for Afrikaans (`af`).
class WidgetsLocalizationAf extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Afrikaans.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationAf() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopieer';

  @override
  String get cutButtonLabel => 'Knip';

  @override
  String get lookUpButtonLabel => 'Kyk op';

  @override
  String get pasteButtonLabel => 'Plak';

  @override
  String get reorderItemDown => 'Skuif af';

  @override
  String get reorderItemLeft => 'Skuif na links';

  @override
  String get reorderItemRight => 'Skuif na regs';

  @override
  String get reorderItemToEnd => 'Skuif na die einde';

  @override
  String get reorderItemToStart => 'Skuif na die begin';

  @override
  String get reorderItemUp => 'Skuif op';

  @override
  String get searchWebButtonLabel => 'Deursoek web';

  @override
  String get selectAllButtonLabel => 'Kies alles';

  @override
  String get shareButtonLabel => 'Deel';
}

/// The translations for Amharic (`am`).
class WidgetsLocalizationAm extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Amharic.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationAm() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'ቅዳ';

  @override
  String get cutButtonLabel => 'ቁረጥ';

  @override
  String get lookUpButtonLabel => 'ይመልከቱ';

  @override
  String get pasteButtonLabel => 'ለጥፍ';

  @override
  String get reorderItemDown => 'ወደ ታች ውሰድ';

  @override
  String get reorderItemLeft => 'ወደ ግራ ውሰድ';

  @override
  String get reorderItemRight => 'ወደ ቀኝ ውሰድ';

  @override
  String get reorderItemToEnd => 'ወደ መጨረሻ ውሰድ';

  @override
  String get reorderItemToStart => 'ወደ መጀመሪያ ውሰድ';

  @override
  String get reorderItemUp => 'ወደ ላይ ውሰድ';

  @override
  String get searchWebButtonLabel => 'ድርን ፈልግ';

  @override
  String get selectAllButtonLabel => 'ሁሉንም ምረጥ';

  @override
  String get shareButtonLabel => 'አጋራ';
}

/// The translations for Arabic (`ar`).
class WidgetsLocalizationAr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Arabic.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationAr() : super(TextDirection.rtl);

  @override
  String get copyButtonLabel => 'نسخ';

  @override
  String get cutButtonLabel => 'قص';

  @override
  String get lookUpButtonLabel => 'بحث عام';

  @override
  String get pasteButtonLabel => 'لصق';

  @override
  String get reorderItemDown => 'نقل لأسفل';

  @override
  String get reorderItemLeft => 'نقل لليمين';

  @override
  String get reorderItemRight => 'نقل لليسار';

  @override
  String get reorderItemToEnd => 'نقل إلى نهاية القائمة';

  @override
  String get reorderItemToStart => 'نقل إلى بداية القائمة';

  @override
  String get reorderItemUp => 'نقل لأعلى';

  @override
  String get searchWebButtonLabel => 'البحث على الويب';

  @override
  String get selectAllButtonLabel => 'اختيار الكل';

  @override
  String get shareButtonLabel => 'مشاركة';
}

/// The translations for Assamese (`as`).
class WidgetsLocalizationAs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Assamese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationAs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'প্ৰতিলিপি কৰক';

  @override
  String get cutButtonLabel => 'কাট কৰক';

  @override
  String get lookUpButtonLabel => 'ওপৰলৈ চাওক';

  @override
  String get pasteButtonLabel => "পে'ষ্ট কৰক";

  @override
  String get reorderItemDown => 'তললৈ স্থানান্তৰ কৰক';

  @override
  String get reorderItemLeft => 'বাওঁফাললৈ স্থানান্তৰ কৰক';

  @override
  String get reorderItemRight => 'সোঁফাললৈ স্থানান্তৰ কৰক';

  @override
  String get reorderItemToEnd => 'শেষলৈ স্থানান্তৰ কৰক';

  @override
  String get reorderItemToStart => 'আৰম্ভণিলৈ স্থানান্তৰ কৰক';

  @override
  String get reorderItemUp => 'ওপৰলৈ নিয়ক';

  @override
  String get searchWebButtonLabel => 'ৱেবত সন্ধান কৰক';

  @override
  String get selectAllButtonLabel => 'সকলো বাছনি কৰক';

  @override
  String get shareButtonLabel => 'শ্বেয়াৰ কৰক';
}

/// The translations for Azerbaijani (`az`).
class WidgetsLocalizationAz extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Azerbaijani.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationAz() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopyalayın';

  @override
  String get cutButtonLabel => 'Kəsin';

  @override
  String get lookUpButtonLabel => 'Axtarın';

  @override
  String get pasteButtonLabel => 'Yerləşdirin';

  @override
  String get reorderItemDown => 'Aşağı köçürün';

  @override
  String get reorderItemLeft => 'Sola köçürün';

  @override
  String get reorderItemRight => 'Sağa köçürün';

  @override
  String get reorderItemToEnd => 'Sona köçürün';

  @override
  String get reorderItemToStart => 'Əvvələ köçürün';

  @override
  String get reorderItemUp => 'Yuxarı köçürün';

  @override
  String get searchWebButtonLabel => 'Vebdə axtarın';

  @override
  String get selectAllButtonLabel => 'Hamısını seçin';

  @override
  String get shareButtonLabel => 'Paylaşın';
}

/// The translations for Belarusian (`be`).
class WidgetsLocalizationBe extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Belarusian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationBe() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Капіраваць';

  @override
  String get cutButtonLabel => 'Выразаць';

  @override
  String get lookUpButtonLabel => 'Знайсці';

  @override
  String get pasteButtonLabel => 'Уставіць';

  @override
  String get reorderItemDown => 'Перамясціць уніз';

  @override
  String get reorderItemLeft => 'Перамясціць улева';

  @override
  String get reorderItemRight => 'Перамясціць управа';

  @override
  String get reorderItemToEnd => 'Перамясціць у канец';

  @override
  String get reorderItemToStart => 'Перамясціць у пачатак';

  @override
  String get reorderItemUp => 'Перамясціць уверх';

  @override
  String get searchWebButtonLabel => 'Пошук у сетцы';

  @override
  String get selectAllButtonLabel => 'Выбраць усе';

  @override
  String get shareButtonLabel => 'Абагуліць';
}

/// The translations for Bulgarian (`bg`).
class WidgetsLocalizationBg extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Bulgarian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationBg() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Копиране';

  @override
  String get cutButtonLabel => 'Изрязване';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'Поставяне';

  @override
  String get reorderItemDown => 'Преместване надолу';

  @override
  String get reorderItemLeft => 'Преместване наляво';

  @override
  String get reorderItemRight => 'Преместване надясно';

  @override
  String get reorderItemToEnd => 'Преместване в края';

  @override
  String get reorderItemToStart => 'Преместване в началото';

  @override
  String get reorderItemUp => 'Преместване нагоре';

  @override
  String get searchWebButtonLabel => 'Търсене в мрежата';

  @override
  String get selectAllButtonLabel => 'Избиране на всички';

  @override
  String get shareButtonLabel => 'Споделяне';
}

/// The translations for Bengali Bangla (`bn`).
class WidgetsLocalizationBn extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Bengali Bangla.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationBn() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'কপি করুন';

  @override
  String get cutButtonLabel => 'কাট করুন';

  @override
  String get lookUpButtonLabel => 'লুক-আপ';

  @override
  String get pasteButtonLabel => 'পেস্ট করুন';

  @override
  String get reorderItemDown => 'নিচের দিকে সরান';

  @override
  String get reorderItemLeft => 'বাঁদিকে সরান';

  @override
  String get reorderItemRight => 'ডানদিকে সরান';

  @override
  String get reorderItemToEnd => 'একদম শেষের দিকে যান';

  @override
  String get reorderItemToStart => 'চালু করতে সরান';

  @override
  String get reorderItemUp => 'উপরের দিকে সরান';

  @override
  String get searchWebButtonLabel => 'ওয়েবে সার্চ করুন';

  @override
  String get selectAllButtonLabel => 'সব বেছে নিন';

  @override
  String get shareButtonLabel => 'শেয়ার করুন';
}

/// The translations for Bosnian (`bs`).
class WidgetsLocalizationBs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Bosnian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationBs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get lookUpButtonLabel => 'Pogled nagore';

  @override
  String get pasteButtonLabel => 'Zalijepi';

  @override
  String get reorderItemDown => 'Pomjeri nadolje';

  @override
  String get reorderItemLeft => 'Pomjeri lijevo';

  @override
  String get reorderItemRight => 'Pomjeri desno';

  @override
  String get reorderItemToEnd => 'Pomjerite na kraj';

  @override
  String get reorderItemToStart => 'Pomjerite na početak';

  @override
  String get reorderItemUp => 'Pomjeri nagore';

  @override
  String get searchWebButtonLabel => 'Pretraži Web';

  @override
  String get selectAllButtonLabel => 'Odaberi sve';

  @override
  String get shareButtonLabel => 'Dijeli';
}

/// The translations for Catalan Valencian (`ca`).
class WidgetsLocalizationCa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Catalan Valencian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationCa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copia';

  @override
  String get cutButtonLabel => 'Retalla';

  @override
  String get lookUpButtonLabel => 'Mira amunt';

  @override
  String get pasteButtonLabel => 'Enganxa';

  @override
  String get reorderItemDown => 'Mou avall';

  @override
  String get reorderItemLeft => "Mou cap a l'esquerra";

  @override
  String get reorderItemRight => 'Mou cap a la dreta';

  @override
  String get reorderItemToEnd => 'Mou al final';

  @override
  String get reorderItemToStart => 'Mou al principi';

  @override
  String get reorderItemUp => 'Mou amunt';

  @override
  String get searchWebButtonLabel => 'Cerca al web';

  @override
  String get selectAllButtonLabel => 'Selecciona-ho tot';

  @override
  String get shareButtonLabel => 'Comparteix';
}

/// The translations for Czech (`cs`).
class WidgetsLocalizationCs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Czech.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationCs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopírovat';

  @override
  String get cutButtonLabel => 'Vyjmout';

  @override
  String get lookUpButtonLabel => 'Vyhledat';

  @override
  String get pasteButtonLabel => 'Vložit';

  @override
  String get reorderItemDown => 'Přesunout dolů';

  @override
  String get reorderItemLeft => 'Přesunout doleva';

  @override
  String get reorderItemRight => 'Přesunout doprava';

  @override
  String get reorderItemToEnd => 'Přesunout na konec';

  @override
  String get reorderItemToStart => 'Přesunout na začátek';

  @override
  String get reorderItemUp => 'Přesunout nahoru';

  @override
  String get searchWebButtonLabel => 'Vyhledávat na webu';

  @override
  String get selectAllButtonLabel => 'Vybrat vše';

  @override
  String get shareButtonLabel => 'Sdílet';
}

/// The translations for Welsh (`cy`).
class WidgetsLocalizationCy extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Welsh.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationCy() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copïo';

  @override
  String get cutButtonLabel => 'Torri';

  @override
  String get lookUpButtonLabel => 'Chwilio';

  @override
  String get pasteButtonLabel => 'Gludo';

  @override
  String get reorderItemDown => 'Symud i lawr';

  @override
  String get reorderItemLeft => "Symud i'r chwith";

  @override
  String get reorderItemRight => "Symud i'r dde";

  @override
  String get reorderItemToEnd => "Symud i'r diwedd";

  @override
  String get reorderItemToStart => "Symud i'r dechrau";

  @override
  String get reorderItemUp => 'Symud i fyny';

  @override
  String get searchWebButtonLabel => "Chwilio'r We";

  @override
  String get selectAllButtonLabel => 'Dewis y Cyfan';

  @override
  String get shareButtonLabel => 'Rhannu';
}

/// The translations for Danish (`da`).
class WidgetsLocalizationDa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Danish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationDa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klip';

  @override
  String get lookUpButtonLabel => 'Slå op';

  @override
  String get pasteButtonLabel => 'Indsæt';

  @override
  String get reorderItemDown => 'Flyt ned';

  @override
  String get reorderItemLeft => 'Flyt til venstre';

  @override
  String get reorderItemRight => 'Flyt til højre';

  @override
  String get reorderItemToEnd => 'Flyt til sidst på listen';

  @override
  String get reorderItemToStart => 'Flyt til først på listen';

  @override
  String get reorderItemUp => 'Flyt op';

  @override
  String get searchWebButtonLabel => 'Søg på nettet';

  @override
  String get selectAllButtonLabel => 'Markér alt';

  @override
  String get shareButtonLabel => 'Del';
}

/// The translations for German (`de`).
class WidgetsLocalizationDe extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for German.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationDe() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopieren';

  @override
  String get cutButtonLabel => 'Ausschneiden';

  @override
  String get lookUpButtonLabel => 'Nachschlagen';

  @override
  String get pasteButtonLabel => 'Einsetzen';

  @override
  String get reorderItemDown => 'Nach unten verschieben';

  @override
  String get reorderItemLeft => 'Nach links verschieben';

  @override
  String get reorderItemRight => 'Nach rechts verschieben';

  @override
  String get reorderItemToEnd => 'An das Ende verschieben';

  @override
  String get reorderItemToStart => 'An den Anfang verschieben';

  @override
  String get reorderItemUp => 'Nach oben verschieben';

  @override
  String get searchWebButtonLabel => 'Im Web suchen';

  @override
  String get selectAllButtonLabel => 'Alle auswählen';

  @override
  String get shareButtonLabel => 'Teilen';
}

/// The translations for German, as used in Switzerland (`de_CH`).
class WidgetsLocalizationDeCh extends WidgetsLocalizationDe {
  /// Create an instance of the translation bundle for German, as used in Switzerland.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationDeCh();
}

/// The translations for Modern Greek (`el`).
class WidgetsLocalizationEl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Modern Greek.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Αντιγραφή';

  @override
  String get cutButtonLabel => 'Αποκοπή';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'Επικόλληση';

  @override
  String get reorderItemDown => 'Μετακίνηση προς τα κάτω';

  @override
  String get reorderItemLeft => 'Μετακίνηση αριστερά';

  @override
  String get reorderItemRight => 'Μετακίνηση δεξιά';

  @override
  String get reorderItemToEnd => 'Μετακίνηση στο τέλος';

  @override
  String get reorderItemToStart => 'Μετακίνηση στην αρχή';

  @override
  String get reorderItemUp => 'Μετακίνηση προς τα πάνω';

  @override
  String get searchWebButtonLabel => 'Αναζήτηση στον ιστό';

  @override
  String get selectAllButtonLabel => 'Επιλογή όλων';

  @override
  String get shareButtonLabel => 'Κοινή χρήση';
}

/// The translations for English (`en`).
class WidgetsLocalizationEn extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for English.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEn() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copy';

  @override
  String get cutButtonLabel => 'Cut';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'Paste';

  @override
  String get reorderItemDown => 'Move down';

  @override
  String get reorderItemLeft => 'Move left';

  @override
  String get reorderItemRight => 'Move right';

  @override
  String get reorderItemToEnd => 'Move to the end';

  @override
  String get reorderItemToStart => 'Move to the start';

  @override
  String get reorderItemUp => 'Move up';

  @override
  String get searchWebButtonLabel => 'Search Web';

  @override
  String get selectAllButtonLabel => 'Select all';

  @override
  String get shareButtonLabel => 'Share';
}

/// The translations for English, as used in Australia (`en_AU`).
class WidgetsLocalizationEnAu extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Australia.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnAu();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in Canada (`en_CA`).
class WidgetsLocalizationEnCa extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Canada.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnCa();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';
}

/// The translations for English, as used in the United Kingdom (`en_GB`).
class WidgetsLocalizationEnGb extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in the United Kingdom.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnGb();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in Ireland (`en_IE`).
class WidgetsLocalizationEnIe extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Ireland.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnIe();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in India (`en_IN`).
class WidgetsLocalizationEnIn extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in India.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnIn();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in New Zealand (`en_NZ`).
class WidgetsLocalizationEnNz extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in New Zealand.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnNz();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in Singapore (`en_SG`).
class WidgetsLocalizationEnSg extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Singapore.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnSg();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for English, as used in South Africa (`en_ZA`).
class WidgetsLocalizationEnZa extends WidgetsLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in South Africa.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEnZa();

  @override
  String get reorderItemLeft => 'Move to the left';

  @override
  String get reorderItemRight => 'Move to the right';

  @override
  String get lookUpButtonLabel => 'Look up';
}

/// The translations for Spanish Castilian (`es`).
class WidgetsLocalizationEs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Spanish Castilian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get lookUpButtonLabel => 'Buscador visual';

  @override
  String get pasteButtonLabel => 'Pegar';

  @override
  String get reorderItemDown => 'Mover hacia abajo';

  @override
  String get reorderItemLeft => 'Mover hacia la izquierda';

  @override
  String get reorderItemRight => 'Mover hacia la derecha';

  @override
  String get reorderItemToEnd => 'Mover al final';

  @override
  String get reorderItemToStart => 'Mover al principio';

  @override
  String get reorderItemUp => 'Mover hacia arriba';

  @override
  String get searchWebButtonLabel => 'Buscar en la Web';

  @override
  String get selectAllButtonLabel => 'Seleccionar todo';

  @override
  String get shareButtonLabel => 'Compartir';
}

/// The translations for Spanish Castilian, as used in Latin America and the Caribbean (`es_419`).
class WidgetsLocalizationEs419 extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Latin America and the Caribbean.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEs419();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Argentina (`es_AR`).
class WidgetsLocalizationEsAr extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Argentina.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsAr();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Bolivia (`es_BO`).
class WidgetsLocalizationEsBo extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Bolivia.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsBo();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Chile (`es_CL`).
class WidgetsLocalizationEsCl extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Chile.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsCl();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Colombia (`es_CO`).
class WidgetsLocalizationEsCo extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Colombia.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsCo();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Costa Rica (`es_CR`).
class WidgetsLocalizationEsCr extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Costa Rica.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsCr();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in the Dominican Republic (`es_DO`).
class WidgetsLocalizationEsDo extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in the Dominican Republic.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsDo();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Ecuador (`es_EC`).
class WidgetsLocalizationEsEc extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Ecuador.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsEc();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Guatemala (`es_GT`).
class WidgetsLocalizationEsGt extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Guatemala.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsGt();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Honduras (`es_HN`).
class WidgetsLocalizationEsHn extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Honduras.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsHn();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Mexico (`es_MX`).
class WidgetsLocalizationEsMx extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Mexico.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsMx();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Nicaragua (`es_NI`).
class WidgetsLocalizationEsNi extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Nicaragua.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsNi();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Panama (`es_PA`).
class WidgetsLocalizationEsPa extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Panama.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsPa();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Peru (`es_PE`).
class WidgetsLocalizationEsPe extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Peru.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsPe();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Puerto Rico (`es_PR`).
class WidgetsLocalizationEsPr extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Puerto Rico.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsPr();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Paraguay (`es_PY`).
class WidgetsLocalizationEsPy extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Paraguay.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsPy();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in El Salvador (`es_SV`).
class WidgetsLocalizationEsSv extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in El Salvador.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsSv();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in the United States (`es_US`).
class WidgetsLocalizationEsUs extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in the United States.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsUs();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Uruguay (`es_UY`).
class WidgetsLocalizationEsUy extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Uruguay.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsUy();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Spanish Castilian, as used in Venezuela (`es_VE`).
class WidgetsLocalizationEsVe extends WidgetsLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Venezuela.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEsVe();

  @override
  String get reorderItemToStart => 'Mover al inicio';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';
}

/// The translations for Estonian (`et`).
class WidgetsLocalizationEt extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Estonian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEt() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopeeri';

  @override
  String get cutButtonLabel => 'Lõika';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'Kleebi';

  @override
  String get reorderItemDown => 'Teisalda alla';

  @override
  String get reorderItemLeft => 'Teisalda vasakule';

  @override
  String get reorderItemRight => 'Teisalda paremale';

  @override
  String get reorderItemToEnd => 'Teisalda lõppu';

  @override
  String get reorderItemToStart => 'Teisalda algusesse';

  @override
  String get reorderItemUp => 'Teisalda üles';

  @override
  String get searchWebButtonLabel => 'Otsi veebist';

  @override
  String get selectAllButtonLabel => 'Vali kõik';

  @override
  String get shareButtonLabel => 'Jagamine';
}

/// The translations for Basque (`eu`).
class WidgetsLocalizationEu extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Basque.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationEu() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiatu';

  @override
  String get cutButtonLabel => 'Ebaki';

  @override
  String get lookUpButtonLabel => 'Bilatu';

  @override
  String get pasteButtonLabel => 'Itsatsi';

  @override
  String get reorderItemDown => 'Eraman behera';

  @override
  String get reorderItemLeft => 'Eraman ezkerrera';

  @override
  String get reorderItemRight => 'Eraman eskuinera';

  @override
  String get reorderItemToEnd => 'Eraman amaierara';

  @override
  String get reorderItemToStart => 'Eraman hasierara';

  @override
  String get reorderItemUp => 'Eraman gora';

  @override
  String get searchWebButtonLabel => 'Bilatu sarean';

  @override
  String get selectAllButtonLabel => 'Hautatu guztiak';

  @override
  String get shareButtonLabel => 'Partekatu';
}

/// The translations for Persian (`fa`).
class WidgetsLocalizationFa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Persian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationFa() : super(TextDirection.rtl);

  @override
  String get copyButtonLabel => 'کپی';

  @override
  String get cutButtonLabel => 'برش';

  @override
  String get lookUpButtonLabel => 'جستجو';

  @override
  String get pasteButtonLabel => 'جای‌گذاری';

  @override
  String get reorderItemDown => 'انتقال به پایین';

  @override
  String get reorderItemLeft => 'انتقال به راست';

  @override
  String get reorderItemRight => 'انتقال به چپ';

  @override
  String get reorderItemToEnd => 'انتقال به انتها';

  @override
  String get reorderItemToStart => 'انتقال به ابتدا';

  @override
  String get reorderItemUp => 'انتقال به بالا';

  @override
  String get searchWebButtonLabel => 'جستجو در وب';

  @override
  String get selectAllButtonLabel => 'انتخاب همه';

  @override
  String get shareButtonLabel => 'هم‌رسانی کردن';
}

/// The translations for Finnish (`fi`).
class WidgetsLocalizationFi extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Finnish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationFi() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopioi';

  @override
  String get cutButtonLabel => 'Leikkaa';

  @override
  String get lookUpButtonLabel => 'Hae';

  @override
  String get pasteButtonLabel => 'Liitä';

  @override
  String get reorderItemDown => 'Siirrä alas';

  @override
  String get reorderItemLeft => 'Siirrä vasemmalle';

  @override
  String get reorderItemRight => 'Siirrä oikealle';

  @override
  String get reorderItemToEnd => 'Siirrä loppuun';

  @override
  String get reorderItemToStart => 'Siirrä alkuun';

  @override
  String get reorderItemUp => 'Siirrä ylös';

  @override
  String get searchWebButtonLabel => 'Hae verkosta';

  @override
  String get selectAllButtonLabel => 'Valitse kaikki';

  @override
  String get shareButtonLabel => 'Jaa';
}

/// The translations for Filipino Pilipino (`fil`).
class WidgetsLocalizationFil extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Filipino Pilipino.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationFil() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopyahin';

  @override
  String get cutButtonLabel => 'I-cut';

  @override
  String get lookUpButtonLabel => 'Tumingin sa Itaas';

  @override
  String get pasteButtonLabel => 'I-paste';

  @override
  String get reorderItemDown => 'Ilipat pababa';

  @override
  String get reorderItemLeft => 'Ilipat pakaliwa';

  @override
  String get reorderItemRight => 'Ilipat pakanan';

  @override
  String get reorderItemToEnd => 'Ilipat sa dulo';

  @override
  String get reorderItemToStart => 'Ilipat sa simula';

  @override
  String get reorderItemUp => 'Ilipat pataas';

  @override
  String get searchWebButtonLabel => 'Maghanap sa Web';

  @override
  String get selectAllButtonLabel => 'Piliin lahat';

  @override
  String get shareButtonLabel => 'I-share';
}

/// The translations for French (`fr`).
class WidgetsLocalizationFr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for French.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationFr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copier';

  @override
  String get cutButtonLabel => 'Couper';

  @override
  String get lookUpButtonLabel => 'Recherche visuelle';

  @override
  String get pasteButtonLabel => 'Coller';

  @override
  String get reorderItemDown => 'Déplacer vers le bas';

  @override
  String get reorderItemLeft => 'Déplacer vers la gauche';

  @override
  String get reorderItemRight => 'Déplacer vers la droite';

  @override
  String get reorderItemToEnd => 'Déplacer vers la fin';

  @override
  String get reorderItemToStart => 'Déplacer vers le début';

  @override
  String get reorderItemUp => 'Déplacer vers le haut';

  @override
  String get searchWebButtonLabel => 'Rechercher sur le Web';

  @override
  String get selectAllButtonLabel => 'Tout sélectionner';

  @override
  String get shareButtonLabel => 'Partager';
}

/// The translations for French, as used in Canada (`fr_CA`).
class WidgetsLocalizationFrCa extends WidgetsLocalizationFr {
  /// Create an instance of the translation bundle for French, as used in Canada.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationFrCa();

  @override
  String get reorderItemToStart => 'Déplacer au début';

  @override
  String get reorderItemToEnd => 'Déplacer à la fin';

  @override
  String get lookUpButtonLabel => 'Regarder en haut';
}

/// The translations for Galician (`gl`).
class WidgetsLocalizationGl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Galician.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationGl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get lookUpButtonLabel => 'Mirar cara arriba';

  @override
  String get pasteButtonLabel => 'Pegar';

  @override
  String get reorderItemDown => 'Mover cara abaixo';

  @override
  String get reorderItemLeft => 'Mover cara á esquerda';

  @override
  String get reorderItemRight => 'Mover cara á dereita';

  @override
  String get reorderItemToEnd => 'Mover ao final';

  @override
  String get reorderItemToStart => 'Mover ao inicio';

  @override
  String get reorderItemUp => 'Mover cara arriba';

  @override
  String get searchWebButtonLabel => 'Buscar na Web';

  @override
  String get selectAllButtonLabel => 'Seleccionar todo';

  @override
  String get shareButtonLabel => 'Compartir';
}

/// The translations for Swiss German Alemannic Alsatian (`gsw`).
class WidgetsLocalizationGsw extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Swiss German Alemannic Alsatian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationGsw() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopieren';

  @override
  String get cutButtonLabel => 'Ausschneiden';

  @override
  String get lookUpButtonLabel => 'Nachschlagen';

  @override
  String get pasteButtonLabel => 'Einsetzen';

  @override
  String get reorderItemDown => 'Nach unten verschieben';

  @override
  String get reorderItemLeft => 'Nach links verschieben';

  @override
  String get reorderItemRight => 'Nach rechts verschieben';

  @override
  String get reorderItemToEnd => 'An das Ende verschieben';

  @override
  String get reorderItemToStart => 'An den Anfang verschieben';

  @override
  String get reorderItemUp => 'Nach oben verschieben';

  @override
  String get searchWebButtonLabel => 'Im Web suchen';

  @override
  String get selectAllButtonLabel => 'Alle auswählen';

  @override
  String get shareButtonLabel => 'Teilen';
}

/// The translations for Gujarati (`gu`).
class WidgetsLocalizationGu extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Gujarati.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationGu() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'કૉપિ કરો';

  @override
  String get cutButtonLabel => 'કાપો';

  @override
  String get lookUpButtonLabel => 'શોધો';

  @override
  String get pasteButtonLabel => 'પેસ્ટ કરો';

  @override
  String get reorderItemDown => 'નીચે ખસેડો';

  @override
  String get reorderItemLeft => 'ડાબે ખસેડો';

  @override
  String get reorderItemRight => 'જમણે ખસેડો';

  @override
  String get reorderItemToEnd => 'અંતમાં ખસેડો';

  @override
  String get reorderItemToStart => 'પ્રારંભમાં ખસેડો';

  @override
  String get reorderItemUp => 'ઉપર ખસેડો';

  @override
  String get searchWebButtonLabel => 'વેબ પર શોધો';

  @override
  String get selectAllButtonLabel => 'બધા પસંદ કરો';

  @override
  String get shareButtonLabel => 'શેર કરો';
}

/// The translations for Hebrew (`he`).
class WidgetsLocalizationHe extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Hebrew.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationHe() : super(TextDirection.rtl);

  @override
  String get copyButtonLabel => 'העתקה';

  @override
  String get cutButtonLabel => 'גזירה';

  @override
  String get lookUpButtonLabel => 'חיפוש';

  @override
  String get pasteButtonLabel => 'הדבקה';

  @override
  String get reorderItemDown => 'העברה למטה';

  @override
  String get reorderItemLeft => 'העברה שמאלה';

  @override
  String get reorderItemRight => 'העברה ימינה';

  @override
  String get reorderItemToEnd => 'העברה לסוף';

  @override
  String get reorderItemToStart => 'העברה להתחלה';

  @override
  String get reorderItemUp => 'העברה למעלה';

  @override
  String get searchWebButtonLabel => 'חיפוש באינטרנט';

  @override
  String get selectAllButtonLabel => 'בחירת הכול';

  @override
  String get shareButtonLabel => 'שיתוף';
}

/// The translations for Hindi (`hi`).
class WidgetsLocalizationHi extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Hindi.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationHi() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'कॉपी करें';

  @override
  String get cutButtonLabel => 'काटें';

  @override
  String get lookUpButtonLabel => 'लुक अप बटन';

  @override
  String get pasteButtonLabel => 'चिपकाएं';

  @override
  String get reorderItemDown => 'नीचे ले जाएं';

  @override
  String get reorderItemLeft => 'बाएं ले जाएं';

  @override
  String get reorderItemRight => 'दाएं ले जाएं';

  @override
  String get reorderItemToEnd => 'आखिर में ले जाएं';

  @override
  String get reorderItemToStart => 'शुरुआत पर ले जाएं';

  @override
  String get reorderItemUp => 'ऊपर ले जाएं';

  @override
  String get searchWebButtonLabel => 'वेब पर खोजें';

  @override
  String get selectAllButtonLabel => 'सभी को चुनें';

  @override
  String get shareButtonLabel => 'शेयर करें';
}

/// The translations for Croatian (`hr`).
class WidgetsLocalizationHr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Croatian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationHr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get lookUpButtonLabel => 'Pogled prema gore';

  @override
  String get pasteButtonLabel => 'Zalijepi';

  @override
  String get reorderItemDown => 'Pomakni prema dolje';

  @override
  String get reorderItemLeft => 'Pomakni ulijevo';

  @override
  String get reorderItemRight => 'Pomakni udesno';

  @override
  String get reorderItemToEnd => 'Premjesti na kraj';

  @override
  String get reorderItemToStart => 'Premjesti na početak';

  @override
  String get reorderItemUp => 'Pomakni prema gore';

  @override
  String get searchWebButtonLabel => 'Pretraži web';

  @override
  String get selectAllButtonLabel => 'Odaberi sve';

  @override
  String get shareButtonLabel => 'Dijeli';
}

/// The translations for Hungarian (`hu`).
class WidgetsLocalizationHu extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Hungarian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationHu() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Másolás';

  @override
  String get cutButtonLabel => 'Kivágás';

  @override
  String get lookUpButtonLabel => 'Felfelé nézés';

  @override
  String get pasteButtonLabel => 'Beillesztés';

  @override
  String get reorderItemDown => 'Áthelyezés lefelé';

  @override
  String get reorderItemLeft => 'Áthelyezés balra';

  @override
  String get reorderItemRight => 'Áthelyezés jobbra';

  @override
  String get reorderItemToEnd => 'Áthelyezés a végére';

  @override
  String get reorderItemToStart => 'Áthelyezés az elejére';

  @override
  String get reorderItemUp => 'Áthelyezés felfelé';

  @override
  String get searchWebButtonLabel => 'Keresés az interneten';

  @override
  String get selectAllButtonLabel => 'Összes kijelölése';

  @override
  String get shareButtonLabel => 'Megosztás';
}

/// The translations for Armenian (`hy`).
class WidgetsLocalizationHy extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Armenian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationHy() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Պատճենել';

  @override
  String get cutButtonLabel => 'Կտրել';

  @override
  String get lookUpButtonLabel => 'Փնտրել';

  @override
  String get pasteButtonLabel => 'Տեղադրել';

  @override
  String get reorderItemDown => 'Տեղափոխել ներքև';

  @override
  String get reorderItemLeft => 'Տեղափոխել ձախ';

  @override
  String get reorderItemRight => 'Տեղափոխել աջ';

  @override
  String get reorderItemToEnd => 'Տեղափոխել վերջ';

  @override
  String get reorderItemToStart => 'Տեղափոխել սկիզբ';

  @override
  String get reorderItemUp => 'Տեղափոխել վերև';

  @override
  String get searchWebButtonLabel => 'Որոնել համացանցում';

  @override
  String get selectAllButtonLabel => 'Նշել բոլորը';

  @override
  String get shareButtonLabel => 'Կիսվել';
}

/// The translations for Indonesian (`id`).
class WidgetsLocalizationId extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Indonesian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationId() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Salin';

  @override
  String get cutButtonLabel => 'Potong';

  @override
  String get lookUpButtonLabel => 'Cari';

  @override
  String get pasteButtonLabel => 'Tempel';

  @override
  String get reorderItemDown => 'Turunkan';

  @override
  String get reorderItemLeft => 'Pindahkan ke kiri';

  @override
  String get reorderItemRight => 'Pindahkan ke kanan';

  @override
  String get reorderItemToEnd => 'Pindahkan ke akhir';

  @override
  String get reorderItemToStart => 'Pindahkan ke awal';

  @override
  String get reorderItemUp => 'Naikkan';

  @override
  String get searchWebButtonLabel => 'Telusuri di Web';

  @override
  String get selectAllButtonLabel => 'Pilih semua';

  @override
  String get shareButtonLabel => 'Bagikan';
}

/// The translations for Icelandic (`is`).
class WidgetsLocalizationIs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Icelandic.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationIs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Afrita';

  @override
  String get cutButtonLabel => 'Klippa';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'Líma';

  @override
  String get reorderItemDown => 'Færa niður';

  @override
  String get reorderItemLeft => 'Færa til vinstri';

  @override
  String get reorderItemRight => 'Færa til hægri';

  @override
  String get reorderItemToEnd => 'Færa aftast';

  @override
  String get reorderItemToStart => 'Færa fremst';

  @override
  String get reorderItemUp => 'Færa upp';

  @override
  String get searchWebButtonLabel => 'Leita á vefnum';

  @override
  String get selectAllButtonLabel => 'Velja allt';

  @override
  String get shareButtonLabel => 'Deila';
}

/// The translations for Italian (`it`).
class WidgetsLocalizationIt extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Italian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationIt() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copia';

  @override
  String get cutButtonLabel => 'Taglia';

  @override
  String get lookUpButtonLabel => 'Cerca';

  @override
  String get pasteButtonLabel => 'Incolla';

  @override
  String get reorderItemDown => 'Sposta giù';

  @override
  String get reorderItemLeft => 'Sposta a sinistra';

  @override
  String get reorderItemRight => 'Sposta a destra';

  @override
  String get reorderItemToEnd => 'Sposta alla fine';

  @override
  String get reorderItemToStart => "Sposta all'inizio";

  @override
  String get reorderItemUp => 'Sposta su';

  @override
  String get searchWebButtonLabel => 'Cerca sul web';

  @override
  String get selectAllButtonLabel => 'Seleziona tutto';

  @override
  String get shareButtonLabel => 'Condividi';
}

/// The translations for Japanese (`ja`).
class WidgetsLocalizationJa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Japanese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationJa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'コピー';

  @override
  String get cutButtonLabel => '切り取り';

  @override
  String get lookUpButtonLabel => '調べる';

  @override
  String get pasteButtonLabel => '貼り付け';

  @override
  String get reorderItemDown => '下に移動';

  @override
  String get reorderItemLeft => '左に移動';

  @override
  String get reorderItemRight => '右に移動';

  @override
  String get reorderItemToEnd => '最後に移動';

  @override
  String get reorderItemToStart => '先頭に移動';

  @override
  String get reorderItemUp => '上に移動';

  @override
  String get searchWebButtonLabel => 'ウェブを検索';

  @override
  String get selectAllButtonLabel => 'すべてを選択';

  @override
  String get shareButtonLabel => '共有';
}

/// The translations for Georgian (`ka`).
class WidgetsLocalizationKa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Georgian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'კოპირება';

  @override
  String get cutButtonLabel => 'ამოჭრა';

  @override
  String get lookUpButtonLabel => 'აიხედეთ ზემოთ';

  @override
  String get pasteButtonLabel => 'ჩასმა';

  @override
  String get reorderItemDown => 'ქვემოთ გადატანა';

  @override
  String get reorderItemLeft => 'მარცხნივ გადატანა';

  @override
  String get reorderItemRight => 'მარჯვნივ გადატანა';

  @override
  String get reorderItemToEnd => 'ბოლოში გადატანა';

  @override
  String get reorderItemToStart => 'დასაწყისში გადატანა';

  @override
  String get reorderItemUp => 'ზემოთ გადატანა';

  @override
  String get searchWebButtonLabel => 'ვებში ძიება';

  @override
  String get selectAllButtonLabel => 'ყველას არჩევა';

  @override
  String get shareButtonLabel => 'გაზიარება';
}

/// The translations for Kazakh (`kk`).
class WidgetsLocalizationKk extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Kazakh.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKk() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Көшіру';

  @override
  String get cutButtonLabel => 'Қию';

  @override
  String get lookUpButtonLabel => 'Іздеу';

  @override
  String get pasteButtonLabel => 'Қою';

  @override
  String get reorderItemDown => 'Төменге жылжыту';

  @override
  String get reorderItemLeft => 'Солға жылжыту';

  @override
  String get reorderItemRight => 'Оңға жылжыту';

  @override
  String get reorderItemToEnd => 'Соңына өту';

  @override
  String get reorderItemToStart => 'Басына өту';

  @override
  String get reorderItemUp => 'Жоғарыға жылжыту';

  @override
  String get searchWebButtonLabel => 'Интернеттен іздеу';

  @override
  String get selectAllButtonLabel => 'Барлығын таңдау';

  @override
  String get shareButtonLabel => 'Бөлісу';
}

/// The translations for Khmer Central Khmer (`km`).
class WidgetsLocalizationKm extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Khmer Central Khmer.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKm() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'ចម្លង';

  @override
  String get cutButtonLabel => 'កាត់';

  @override
  String get lookUpButtonLabel => 'រកមើល';

  @override
  String get pasteButtonLabel => 'ដាក់​ចូល';

  @override
  String get reorderItemDown => 'ផ្លាស់ទី​ចុះ​ក្រោម';

  @override
  String get reorderItemLeft => 'ផ្លាស់ទី​ទៅ​ឆ្វេង';

  @override
  String get reorderItemRight => 'ផ្លាស់ទីទៅ​ស្តាំ';

  @override
  String get reorderItemToEnd => 'ផ្លាស់ទីទៅ​ចំណុចបញ្ចប់';

  @override
  String get reorderItemToStart => 'ផ្លាស់ទីទៅ​ចំណុច​ចាប់ផ្ដើម';

  @override
  String get reorderItemUp => 'ផ្លាស់ទី​ឡើង​លើ';

  @override
  String get searchWebButtonLabel => 'ស្វែងរក​លើបណ្ដាញ';

  @override
  String get selectAllButtonLabel => 'ជ្រើសរើស​ទាំងអស់';

  @override
  String get shareButtonLabel => 'ចែករំលែក';
}

/// The translations for Kannada (`kn`).
class WidgetsLocalizationKn extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Kannada.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKn() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => '\u{ca8}\u{c95}\u{cb2}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get cutButtonLabel => '\u{c95}\u{ca4}\u{ccd}\u{ca4}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get lookUpButtonLabel => '\u{cae}\u{cc7}\u{cb2}\u{cc6}\u{20}\u{ca8}\u{ccb}\u{ca1}\u{cbf}';

  @override
  String get pasteButtonLabel => '\u{c85}\u{c82}\u{c9f}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemDown => '\u{c95}\u{cc6}\u{cb3}\u{c97}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemLeft => '\u{c8e}\u{ca1}\u{c95}\u{ccd}\u{c95}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemRight => '\u{cac}\u{cb2}\u{c95}\u{ccd}\u{c95}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemToEnd => '\u{c95}\u{cca}\u{ca8}\u{cc6}\u{c97}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemToStart => '\u{caa}\u{ccd}\u{cb0}\u{cbe}\u{cb0}\u{c82}\u{cad}\u{c95}\u{ccd}\u{c95}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get reorderItemUp => '\u{cae}\u{cc7}\u{cb2}\u{cc6}\u{20}\u{cb8}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get searchWebButtonLabel => '\u{cb5}\u{cc6}\u{cac}\u{ccd}\u{200c}\u{ca8}\u{cb2}\u{ccd}\u{cb2}\u{cbf}\u{20}\u{cb9}\u{cc1}\u{ca1}\u{cc1}\u{c95}\u{cbf}';

  @override
  String get selectAllButtonLabel => '\u{c8e}\u{cb2}\u{ccd}\u{cb2}\u{cb5}\u{ca8}\u{ccd}\u{ca8}\u{cc2}\u{20}\u{c86}\u{caf}\u{ccd}\u{c95}\u{cc6}\u{20}\u{cae}\u{cbe}\u{ca1}\u{cbf}';

  @override
  String get shareButtonLabel => '\u{cb9}\u{c82}\u{c9a}\u{cbf}\u{c95}\u{cca}\u{cb3}\u{ccd}\u{cb3}\u{cbf}';
}

/// The translations for Korean (`ko`).
class WidgetsLocalizationKo extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Korean.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKo() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => '복사';

  @override
  String get cutButtonLabel => '잘라내기';

  @override
  String get lookUpButtonLabel => '찾기';

  @override
  String get pasteButtonLabel => '붙여넣기';

  @override
  String get reorderItemDown => '아래로 이동';

  @override
  String get reorderItemLeft => '왼쪽으로 이동';

  @override
  String get reorderItemRight => '오른쪽으로 이동';

  @override
  String get reorderItemToEnd => '끝으로 이동';

  @override
  String get reorderItemToStart => '시작으로 이동';

  @override
  String get reorderItemUp => '위로 이동';

  @override
  String get searchWebButtonLabel => '웹 검색';

  @override
  String get selectAllButtonLabel => '전체 선택';

  @override
  String get shareButtonLabel => '공유';
}

/// The translations for Kirghiz Kyrgyz (`ky`).
class WidgetsLocalizationKy extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Kirghiz Kyrgyz.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationKy() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Көчүрүү';

  @override
  String get cutButtonLabel => 'Кесүү';

  @override
  String get lookUpButtonLabel => 'Издөө';

  @override
  String get pasteButtonLabel => 'Чаптоо';

  @override
  String get reorderItemDown => 'Төмөн жылдыруу';

  @override
  String get reorderItemLeft => 'Солго жылдыруу';

  @override
  String get reorderItemRight => 'Оңго жылдыруу';

  @override
  String get reorderItemToEnd => 'Аягына жылдыруу';

  @override
  String get reorderItemToStart => 'Башына жылдыруу';

  @override
  String get reorderItemUp => 'Жогору жылдыруу';

  @override
  String get searchWebButtonLabel => 'Интернеттен издөө';

  @override
  String get selectAllButtonLabel => 'Баарын тандоо';

  @override
  String get shareButtonLabel => 'Бөлүшүү';
}

/// The translations for Lao (`lo`).
class WidgetsLocalizationLo extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Lao.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationLo() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'ສຳເນົາ';

  @override
  String get cutButtonLabel => 'ຕັດ';

  @override
  String get lookUpButtonLabel => 'ຊອກຫາຂໍ້ມູນ';

  @override
  String get pasteButtonLabel => 'ວາງ';

  @override
  String get reorderItemDown => 'ຍ້າຍລົງ';

  @override
  String get reorderItemLeft => 'ຍ້າຍໄປຊ້າຍ';

  @override
  String get reorderItemRight => 'ຍ້າຍໄປຂວາ';

  @override
  String get reorderItemToEnd => 'ຍ້າຍໄປສິ້ນສຸດ';

  @override
  String get reorderItemToStart => 'ຍ້າຍໄປເລີ່ມຕົ້ນ';

  @override
  String get reorderItemUp => 'ຍ້າຍຂຶ້ນ';

  @override
  String get searchWebButtonLabel => 'ຊອກຫາຢູ່ອິນເຕີເນັດ';

  @override
  String get selectAllButtonLabel => 'ເລືອກທັງໝົດ';

  @override
  String get shareButtonLabel => 'ແບ່ງປັນ';
}

/// The translations for Lithuanian (`lt`).
class WidgetsLocalizationLt extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Lithuanian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationLt() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopijuoti';

  @override
  String get cutButtonLabel => 'Iškirpti';

  @override
  String get lookUpButtonLabel => 'Ieškoti';

  @override
  String get pasteButtonLabel => 'Įklijuoti';

  @override
  String get reorderItemDown => 'Perkelti žemyn';

  @override
  String get reorderItemLeft => 'Perkelti kairėn';

  @override
  String get reorderItemRight => 'Perkelti dešinėn';

  @override
  String get reorderItemToEnd => 'Perkelti į pabaigą';

  @override
  String get reorderItemToStart => 'Perkelti į pradžią';

  @override
  String get reorderItemUp => 'Perkelti aukštyn';

  @override
  String get searchWebButtonLabel => 'Ieškoti žiniatinklyje';

  @override
  String get selectAllButtonLabel => 'Pasirinkti viską';

  @override
  String get shareButtonLabel => 'Bendrinti';
}

/// The translations for Latvian (`lv`).
class WidgetsLocalizationLv extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Latvian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationLv() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopēt';

  @override
  String get cutButtonLabel => 'Izgriezt';

  @override
  String get lookUpButtonLabel => 'Meklēt';

  @override
  String get pasteButtonLabel => 'Ielīmēt';

  @override
  String get reorderItemDown => 'Pārvietot uz leju';

  @override
  String get reorderItemLeft => 'Pārvietot pa kreisi';

  @override
  String get reorderItemRight => 'Pārvietot pa labi';

  @override
  String get reorderItemToEnd => 'Pārvietot uz beigām';

  @override
  String get reorderItemToStart => 'Pārvietot uz sākumu';

  @override
  String get reorderItemUp => 'Pārvietot uz augšu';

  @override
  String get searchWebButtonLabel => 'Meklēt tīmeklī';

  @override
  String get selectAllButtonLabel => 'Atlasīt visu';

  @override
  String get shareButtonLabel => 'Kopīgot';
}

/// The translations for Macedonian (`mk`).
class WidgetsLocalizationMk extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Macedonian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMk() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Копирај';

  @override
  String get cutButtonLabel => 'Исечи';

  @override
  String get lookUpButtonLabel => 'Погледнете нагоре';

  @override
  String get pasteButtonLabel => 'Залепи';

  @override
  String get reorderItemDown => 'Преместете надолу';

  @override
  String get reorderItemLeft => 'Преместете налево';

  @override
  String get reorderItemRight => 'Преместете надесно';

  @override
  String get reorderItemToEnd => 'Преместете на крајот';

  @override
  String get reorderItemToStart => 'Преместете на почеток';

  @override
  String get reorderItemUp => 'Преместете нагоре';

  @override
  String get searchWebButtonLabel => 'Пребарајте на интернет';

  @override
  String get selectAllButtonLabel => 'Избери ги сите';

  @override
  String get shareButtonLabel => 'Сподели';
}

/// The translations for Malayalam (`ml`).
class WidgetsLocalizationMl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Malayalam.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'പകർത്തുക';

  @override
  String get cutButtonLabel => 'മുറിക്കുക';

  @override
  String get lookUpButtonLabel => 'മുകളിലേക്ക് നോക്കുക';

  @override
  String get pasteButtonLabel => 'ഒട്ടിക്കുക';

  @override
  String get reorderItemDown => 'താഴോട്ട് നീക്കുക';

  @override
  String get reorderItemLeft => 'ഇടത്തോട്ട് നീക്കുക';

  @override
  String get reorderItemRight => 'വലത്തോട്ട് നീക്കുക';

  @override
  String get reorderItemToEnd => 'അവസാന ഭാഗത്തേക്ക് പോവുക';

  @override
  String get reorderItemToStart => 'തുടക്കത്തിലേക്ക് പോവുക';

  @override
  String get reorderItemUp => 'മുകളിലോട്ട് നീക്കുക';

  @override
  String get searchWebButtonLabel => 'വെബിൽ തിരയുക';

  @override
  String get selectAllButtonLabel => 'എല്ലാം തിരഞ്ഞെടുക്കുക';

  @override
  String get shareButtonLabel => 'പങ്കിടുക';
}

/// The translations for Mongolian (`mn`).
class WidgetsLocalizationMn extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Mongolian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMn() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Хуулах';

  @override
  String get cutButtonLabel => 'Таслах';

  @override
  String get lookUpButtonLabel => 'Дээшээ харах';

  @override
  String get pasteButtonLabel => 'Буулгах';

  @override
  String get reorderItemDown => 'Доош зөөх';

  @override
  String get reorderItemLeft => 'Зүүн тийш зөөх';

  @override
  String get reorderItemRight => 'Баруун тийш зөөх';

  @override
  String get reorderItemToEnd => 'Төгсгөл рүү зөөх';

  @override
  String get reorderItemToStart => 'Эхлэл рүү зөөх';

  @override
  String get reorderItemUp => 'Дээш зөөх';

  @override
  String get searchWebButtonLabel => 'Вебээс хайх';

  @override
  String get selectAllButtonLabel => 'Бүгдийг сонгох';

  @override
  String get shareButtonLabel => 'Хуваалцах';
}

/// The translations for Marathi (`mr`).
class WidgetsLocalizationMr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Marathi.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'कॉपी करा';

  @override
  String get cutButtonLabel => 'कट करा';

  @override
  String get lookUpButtonLabel => 'शोध घ्या';

  @override
  String get pasteButtonLabel => 'पेस्ट करा';

  @override
  String get reorderItemDown => 'खाली हलवा';

  @override
  String get reorderItemLeft => 'डावीकडे हलवा';

  @override
  String get reorderItemRight => 'उजवीकडे हलवा';

  @override
  String get reorderItemToEnd => 'शेवटाकडे हलवा';

  @override
  String get reorderItemToStart => 'सुरुवातीला हलवा';

  @override
  String get reorderItemUp => 'वर हलवा';

  @override
  String get searchWebButtonLabel => 'वेबवर शोधा';

  @override
  String get selectAllButtonLabel => 'सर्व निवडा';

  @override
  String get shareButtonLabel => 'शेअर करा';
}

/// The translations for Malay (`ms`).
class WidgetsLocalizationMs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Malay.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMs() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Salin';

  @override
  String get cutButtonLabel => 'Potong';

  @override
  String get lookUpButtonLabel => 'Lihat ke Atas';

  @override
  String get pasteButtonLabel => 'Tampal';

  @override
  String get reorderItemDown => 'Alih ke bawah';

  @override
  String get reorderItemLeft => 'Alih ke kiri';

  @override
  String get reorderItemRight => 'Alih ke kanan';

  @override
  String get reorderItemToEnd => 'Alih ke penghujung';

  @override
  String get reorderItemToStart => 'Alih ke permulaan';

  @override
  String get reorderItemUp => 'Alih ke atas';

  @override
  String get searchWebButtonLabel => 'Buat carian pada Web';

  @override
  String get selectAllButtonLabel => 'Pilih semua';

  @override
  String get shareButtonLabel => 'Kongsi';
}

/// The translations for Burmese (`my`).
class WidgetsLocalizationMy extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Burmese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationMy() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'မိတ္တူကူးရန်';

  @override
  String get cutButtonLabel => 'ဖြတ်ယူရန်';

  @override
  String get lookUpButtonLabel => 'အပေါ်ကြည့်ရန်';

  @override
  String get pasteButtonLabel => 'ကူးထည့်ရန်';

  @override
  String get reorderItemDown => 'အောက်သို့ရွှေ့ရန်';

  @override
  String get reorderItemLeft => 'ဘယ်ဘက်သို့ရွှေ့ရန်';

  @override
  String get reorderItemRight => 'ညာဘက်သို့ရွှေ့ရန်';

  @override
  String get reorderItemToEnd => 'အဆုံးသို့ ‌ရွှေ့ရန်';

  @override
  String get reorderItemToStart => 'အစသို့ ရွှေ့ရန်';

  @override
  String get reorderItemUp => 'အပေါ်သို့ ရွှေ့ရန်';

  @override
  String get searchWebButtonLabel => 'ဝဘ်တွင်ရှာရန်';

  @override
  String get selectAllButtonLabel => 'အားလုံး ရွေးရန်';

  @override
  String get shareButtonLabel => 'မျှဝေရန်';
}

/// The translations for Norwegian Bokmål (`nb`).
class WidgetsLocalizationNb extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Norwegian Bokmål.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationNb() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get lookUpButtonLabel => 'Slå opp';

  @override
  String get pasteButtonLabel => 'Lim inn';

  @override
  String get reorderItemDown => 'Flytt ned';

  @override
  String get reorderItemLeft => 'Flytt til venstre';

  @override
  String get reorderItemRight => 'Flytt til høyre';

  @override
  String get reorderItemToEnd => 'Flytt til slutten';

  @override
  String get reorderItemToStart => 'Flytt til starten';

  @override
  String get reorderItemUp => 'Flytt opp';

  @override
  String get searchWebButtonLabel => 'Søk på nettet';

  @override
  String get selectAllButtonLabel => 'Velg alle';

  @override
  String get shareButtonLabel => 'Del';
}

/// The translations for Nepali (`ne`).
class WidgetsLocalizationNe extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Nepali.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationNe() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'कपी गर्नुहोस्';

  @override
  String get cutButtonLabel => 'काट्नुहोस्';

  @override
  String get lookUpButtonLabel => 'माथितिर हेर्नुहोस्';

  @override
  String get pasteButtonLabel => 'टाँस्नुहोस्';

  @override
  String get reorderItemDown => 'तल सार्नुहोस्';

  @override
  String get reorderItemLeft => 'बायाँ सार्नुहोस्';

  @override
  String get reorderItemRight => 'दायाँ सार्नुहोस्';

  @override
  String get reorderItemToEnd => 'अन्त्यमा जानुहोस्';

  @override
  String get reorderItemToStart => 'सुरुमा सार्नुहोस्';

  @override
  String get reorderItemUp => 'माथि सार्नुहोस्';

  @override
  String get searchWebButtonLabel => 'वेबमा खोज्नुहोस्';

  @override
  String get selectAllButtonLabel => 'सबै बटनहरू चयन गर्नुहोस्';

  @override
  String get shareButtonLabel => 'सेयर गर्नुहोस्';
}

/// The translations for Dutch Flemish (`nl`).
class WidgetsLocalizationNl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Dutch Flemish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationNl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiëren';

  @override
  String get cutButtonLabel => 'Knippen';

  @override
  String get lookUpButtonLabel => 'Opzoeken';

  @override
  String get pasteButtonLabel => 'Plakken';

  @override
  String get reorderItemDown => 'Omlaag verplaatsen';

  @override
  String get reorderItemLeft => 'Naar links verplaatsen';

  @override
  String get reorderItemRight => 'Naar rechts verplaatsen';

  @override
  String get reorderItemToEnd => 'Naar het einde verplaatsen';

  @override
  String get reorderItemToStart => 'Naar het begin verplaatsen';

  @override
  String get reorderItemUp => 'Omhoog verplaatsen';

  @override
  String get searchWebButtonLabel => 'Op internet zoeken';

  @override
  String get selectAllButtonLabel => 'Alles selecteren';

  @override
  String get shareButtonLabel => 'Delen';
}

/// The translations for Norwegian (`no`).
class WidgetsLocalizationNo extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Norwegian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationNo() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get lookUpButtonLabel => 'Slå opp';

  @override
  String get pasteButtonLabel => 'Lim inn';

  @override
  String get reorderItemDown => 'Flytt ned';

  @override
  String get reorderItemLeft => 'Flytt til venstre';

  @override
  String get reorderItemRight => 'Flytt til høyre';

  @override
  String get reorderItemToEnd => 'Flytt til slutten';

  @override
  String get reorderItemToStart => 'Flytt til starten';

  @override
  String get reorderItemUp => 'Flytt opp';

  @override
  String get searchWebButtonLabel => 'Søk på nettet';

  @override
  String get selectAllButtonLabel => 'Velg alle';

  @override
  String get shareButtonLabel => 'Del';
}

/// The translations for Oriya (`or`).
class WidgetsLocalizationOr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Oriya.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationOr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'କପି କରନ୍ତୁ';

  @override
  String get cutButtonLabel => 'କଟ୍ କରନ୍ତୁ';

  @override
  String get lookUpButtonLabel => 'ଉପରକୁ ଦେଖନ୍ତୁ';

  @override
  String get pasteButtonLabel => 'ପେଷ୍ଟ କରନ୍ତୁ';

  @override
  String get reorderItemDown => 'ତଳକୁ ଯାଆନ୍ତୁ';

  @override
  String get reorderItemLeft => 'ବାମକୁ ଯାଆନ୍ତୁ';

  @override
  String get reorderItemRight => 'ଡାହାଣକୁ ଯାଆନ୍ତୁ';

  @override
  String get reorderItemToEnd => 'ଶେଷକୁ ଯାଆନ୍ତୁ';

  @override
  String get reorderItemToStart => 'ଆରମ୍ଭକୁ ଯାଆନ୍ତୁ';

  @override
  String get reorderItemUp => 'ଉପରକୁ ନିଅନ୍ତୁ';

  @override
  String get searchWebButtonLabel => 'ୱେବ ସର୍ଚ୍ଚ କରନ୍ତୁ';

  @override
  String get selectAllButtonLabel => 'ସବୁ ଚୟନ କରନ୍ତୁ';

  @override
  String get shareButtonLabel => 'ସେୟାର କରନ୍ତୁ';
}

/// The translations for Panjabi Punjabi (`pa`).
class WidgetsLocalizationPa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Panjabi Punjabi.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationPa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'ਕਾਪੀ ਕਰੋ';

  @override
  String get cutButtonLabel => 'ਕੱਟ ਕਰੋ';

  @override
  String get lookUpButtonLabel => 'ਖੋਜੋ';

  @override
  String get pasteButtonLabel => 'ਪੇਸਟ ਕਰੋ';

  @override
  String get reorderItemDown => 'ਹੇਠਾਂ ਲਿਜਾਓ';

  @override
  String get reorderItemLeft => 'ਖੱਬੇ ਲਿਜਾਓ';

  @override
  String get reorderItemRight => 'ਸੱਜੇ ਲਿਜਾਓ';

  @override
  String get reorderItemToEnd => 'ਅੰਤ ਵਿੱਚ ਲਿਜਾਓ';

  @override
  String get reorderItemToStart => 'ਸ਼ੁਰੂ ਵਿੱਚ ਲਿਜਾਓ';

  @override
  String get reorderItemUp => 'ਉੱਪਰ ਲਿਜਾਓ';

  @override
  String get searchWebButtonLabel => "ਵੈੱਬ 'ਤੇ ਖੋਜੋ";

  @override
  String get selectAllButtonLabel => 'ਸਭ ਚੁਣੋ';

  @override
  String get shareButtonLabel => 'ਸਾਂਝਾ ਕਰੋ';
}

/// The translations for Polish (`pl`).
class WidgetsLocalizationPl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Polish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationPl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiuj';

  @override
  String get cutButtonLabel => 'Wytnij';

  @override
  String get lookUpButtonLabel => 'Sprawdź';

  @override
  String get pasteButtonLabel => 'Wklej';

  @override
  String get reorderItemDown => 'Przenieś w dół';

  @override
  String get reorderItemLeft => 'Przenieś w lewo';

  @override
  String get reorderItemRight => 'Przenieś w prawo';

  @override
  String get reorderItemToEnd => 'Przenieś na koniec';

  @override
  String get reorderItemToStart => 'Przenieś na początek';

  @override
  String get reorderItemUp => 'Przenieś w górę';

  @override
  String get searchWebButtonLabel => 'Szukaj w internecie';

  @override
  String get selectAllButtonLabel => 'Zaznacz wszystko';

  @override
  String get shareButtonLabel => 'Udostępnij';
}

/// The translations for Pushto Pashto (`ps`).
class WidgetsLocalizationPs extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Pushto Pashto.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationPs() : super(TextDirection.rtl);

  @override
  String get copyButtonLabel => 'کاپی';

  @override
  String get cutButtonLabel => 'کم کړئ';

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get pasteButtonLabel => 'پیټ کړئ';

  @override
  String get reorderItemDown => 'Move down';

  @override
  String get reorderItemLeft => 'Move left';

  @override
  String get reorderItemRight => 'Move right';

  @override
  String get reorderItemToEnd => 'Move to the end';

  @override
  String get reorderItemToStart => 'Move to the start';

  @override
  String get reorderItemUp => 'Move up';

  @override
  String get searchWebButtonLabel => 'Search Web';

  @override
  String get selectAllButtonLabel => 'غوره کړئ';

  @override
  String get shareButtonLabel => 'Share...';
}

/// The translations for Portuguese (`pt`).
class WidgetsLocalizationPt extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Portuguese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationPt() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get lookUpButtonLabel => 'Pesquisar';

  @override
  String get pasteButtonLabel => 'Colar';

  @override
  String get reorderItemDown => 'Mover para baixo';

  @override
  String get reorderItemLeft => 'Mover para a esquerda';

  @override
  String get reorderItemRight => 'Mover para a direita';

  @override
  String get reorderItemToEnd => 'Mover para o final';

  @override
  String get reorderItemToStart => 'Mover para o início';

  @override
  String get reorderItemUp => 'Mover para cima';

  @override
  String get searchWebButtonLabel => 'Pesquisar na Web';

  @override
  String get selectAllButtonLabel => 'Selecionar tudo';

  @override
  String get shareButtonLabel => 'Compartilhar';
}

/// The translations for Portuguese, as used in Portugal (`pt_PT`).
class WidgetsLocalizationPtPt extends WidgetsLocalizationPt {
  /// Create an instance of the translation bundle for Portuguese, as used in Portugal.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationPtPt();

  @override
  String get reorderItemToEnd => 'Mover para o fim';

  @override
  String get lookUpButtonLabel => 'Procurar';

  @override
  String get shareButtonLabel => 'Partilhar';
}

/// The translations for Romanian Moldavian Moldovan (`ro`).
class WidgetsLocalizationRo extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Romanian Moldavian Moldovan.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationRo() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Copiați';

  @override
  String get cutButtonLabel => 'Decupați';

  @override
  String get lookUpButtonLabel => 'Privire în sus';

  @override
  String get pasteButtonLabel => 'Inserați';

  @override
  String get reorderItemDown => 'Mutați în jos';

  @override
  String get reorderItemLeft => 'Mutați la stânga';

  @override
  String get reorderItemRight => 'Mutați la dreapta';

  @override
  String get reorderItemToEnd => 'Mutați la sfârșit';

  @override
  String get reorderItemToStart => 'Mutați la început';

  @override
  String get reorderItemUp => 'Mutați în sus';

  @override
  String get searchWebButtonLabel => 'Căutați pe web';

  @override
  String get selectAllButtonLabel => 'Selectați tot';

  @override
  String get shareButtonLabel => 'Trimiteți';
}

/// The translations for Russian (`ru`).
class WidgetsLocalizationRu extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Russian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationRu() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Копировать';

  @override
  String get cutButtonLabel => 'Вырезать';

  @override
  String get lookUpButtonLabel => 'Найти';

  @override
  String get pasteButtonLabel => 'Вставить';

  @override
  String get reorderItemDown => 'Переместить вниз';

  @override
  String get reorderItemLeft => 'Переместить влево';

  @override
  String get reorderItemRight => 'Переместить вправо';

  @override
  String get reorderItemToEnd => 'Переместить в конец';

  @override
  String get reorderItemToStart => 'Переместить в начало';

  @override
  String get reorderItemUp => 'Переместить вверх';

  @override
  String get searchWebButtonLabel => 'Искать в интернете';

  @override
  String get selectAllButtonLabel => 'Выбрать все';

  @override
  String get shareButtonLabel => 'Поделиться';
}

/// The translations for Sinhala Sinhalese (`si`).
class WidgetsLocalizationSi extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Sinhala Sinhalese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSi() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'පිටපත් කරන්න';

  @override
  String get cutButtonLabel => 'කපන්න';

  @override
  String get lookUpButtonLabel => 'උඩ බලන්න';

  @override
  String get pasteButtonLabel => 'අලවන්න';

  @override
  String get reorderItemDown => 'පහළට ගෙන යන්න';

  @override
  String get reorderItemLeft => 'වමට ගෙන යන්න';

  @override
  String get reorderItemRight => 'දකුණට ගෙන යන්න';

  @override
  String get reorderItemToEnd => 'අවසානයට යන්න';

  @override
  String get reorderItemToStart => 'ආරම්භය වෙත යන්න';

  @override
  String get reorderItemUp => 'ඉහළට ගෙන යන්න';

  @override
  String get searchWebButtonLabel => 'වෙබය සොයන්න';

  @override
  String get selectAllButtonLabel => 'සියල්ල තෝරන්න';

  @override
  String get shareButtonLabel => 'බෙදා ගන්න';
}

/// The translations for Slovak (`sk`).
class WidgetsLocalizationSk extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Slovak.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSk() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopírovať';

  @override
  String get cutButtonLabel => 'Vystrihnúť';

  @override
  String get lookUpButtonLabel => 'Pohľad nahor';

  @override
  String get pasteButtonLabel => 'Prilepiť';

  @override
  String get reorderItemDown => 'Presunúť nadol';

  @override
  String get reorderItemLeft => 'Presunúť doľava';

  @override
  String get reorderItemRight => 'Presunúť doprava';

  @override
  String get reorderItemToEnd => 'Presunúť na koniec';

  @override
  String get reorderItemToStart => 'Presunúť na začiatok';

  @override
  String get reorderItemUp => 'Presunúť nahor';

  @override
  String get searchWebButtonLabel => 'Hľadať na webe';

  @override
  String get selectAllButtonLabel => 'Vybrať všetko';

  @override
  String get shareButtonLabel => 'Zdieľať';
}

/// The translations for Slovenian (`sl`).
class WidgetsLocalizationSl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Slovenian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get lookUpButtonLabel => 'Pogled gor';

  @override
  String get pasteButtonLabel => 'Prilepi';

  @override
  String get reorderItemDown => 'Premakni navzdol';

  @override
  String get reorderItemLeft => 'Premakni levo';

  @override
  String get reorderItemRight => 'Premakni desno';

  @override
  String get reorderItemToEnd => 'Premakni na konec';

  @override
  String get reorderItemToStart => 'Premakni na začetek';

  @override
  String get reorderItemUp => 'Premakni navzgor';

  @override
  String get searchWebButtonLabel => 'Iskanje v spletu';

  @override
  String get selectAllButtonLabel => 'Izberi vse';

  @override
  String get shareButtonLabel => 'Deli';
}

/// The translations for Albanian (`sq`).
class WidgetsLocalizationSq extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Albanian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSq() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopjo';

  @override
  String get cutButtonLabel => 'Prit';

  @override
  String get lookUpButtonLabel => 'Kërko';

  @override
  String get pasteButtonLabel => 'Ngjit';

  @override
  String get reorderItemDown => 'Lëvize poshtë';

  @override
  String get reorderItemLeft => 'Lëvize majtas';

  @override
  String get reorderItemRight => 'Lëvize djathtas';

  @override
  String get reorderItemToEnd => 'Lëvize në fund';

  @override
  String get reorderItemToStart => 'Lëvize në fillim';

  @override
  String get reorderItemUp => 'Lëvize lart';

  @override
  String get searchWebButtonLabel => 'Kërko në ueb';

  @override
  String get selectAllButtonLabel => 'Zgjidh të gjitha';

  @override
  String get shareButtonLabel => 'Ndaj';
}

/// The translations for Serbian (`sr`).
class WidgetsLocalizationSr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Serbian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Копирај';

  @override
  String get cutButtonLabel => 'Исеци';

  @override
  String get lookUpButtonLabel => 'Поглед нагоре';

  @override
  String get pasteButtonLabel => 'Налепи';

  @override
  String get reorderItemDown => 'Померите надоле';

  @override
  String get reorderItemLeft => 'Померите улево';

  @override
  String get reorderItemRight => 'Померите удесно';

  @override
  String get reorderItemToEnd => 'Померите на крај';

  @override
  String get reorderItemToStart => 'Померите на почетак';

  @override
  String get reorderItemUp => 'Померите нагоре';

  @override
  String get searchWebButtonLabel => 'Претражи веб';

  @override
  String get selectAllButtonLabel => 'Изабери све';

  @override
  String get shareButtonLabel => 'Дели';
}

/// The translations for Serbian, using the Cyrillic script (`sr_Cyrl`).
class WidgetsLocalizationSrCyrl extends WidgetsLocalizationSr {
  /// Create an instance of the translation bundle for Serbian, using the Cyrillic script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSrCyrl();
}

/// The translations for Serbian, using the Latin script (`sr_Latn`).
class WidgetsLocalizationSrLatn extends WidgetsLocalizationSr {
  /// Create an instance of the translation bundle for Serbian, using the Latin script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSrLatn();

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Iseci';

  @override
  String get lookUpButtonLabel => 'Pogled nagore';

  @override
  String get pasteButtonLabel => 'Nalepi';

  @override
  String get reorderItemDown => 'Pomerite nadole';

  @override
  String get reorderItemLeft => 'Pomerite ulevo';

  @override
  String get reorderItemRight => 'Pomerite udesno';

  @override
  String get reorderItemToEnd => 'Pomerite na kraj';

  @override
  String get reorderItemToStart => 'Pomerite na početak';

  @override
  String get reorderItemUp => 'Pomerite nagore';

  @override
  String get searchWebButtonLabel => 'Pretraži veb';

  @override
  String get selectAllButtonLabel => 'Izaberi sve';

  @override
  String get shareButtonLabel => 'Deli';
}

/// The translations for Swedish (`sv`).
class WidgetsLocalizationSv extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Swedish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSv() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopiera';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get lookUpButtonLabel => 'Titta upp';

  @override
  String get pasteButtonLabel => 'Klistra in';

  @override
  String get reorderItemDown => 'Flytta nedåt';

  @override
  String get reorderItemLeft => 'Flytta åt vänster';

  @override
  String get reorderItemRight => 'Flytta åt höger';

  @override
  String get reorderItemToEnd => 'Flytta till slutet';

  @override
  String get reorderItemToStart => 'Flytta till början';

  @override
  String get reorderItemUp => 'Flytta uppåt';

  @override
  String get searchWebButtonLabel => 'Sök på webben';

  @override
  String get selectAllButtonLabel => 'Markera allt';

  @override
  String get shareButtonLabel => 'Dela';
}

/// The translations for Swahili (`sw`).
class WidgetsLocalizationSw extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Swahili.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationSw() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Nakili';

  @override
  String get cutButtonLabel => 'Kata';

  @override
  String get lookUpButtonLabel => 'Tafuta';

  @override
  String get pasteButtonLabel => 'Bandika';

  @override
  String get reorderItemDown => 'Sogeza chini';

  @override
  String get reorderItemLeft => 'Sogeza kushoto';

  @override
  String get reorderItemRight => 'Sogeza kulia';

  @override
  String get reorderItemToEnd => 'Sogeza hadi mwisho';

  @override
  String get reorderItemToStart => 'Sogeza hadi mwanzo';

  @override
  String get reorderItemUp => 'Sogeza juu';

  @override
  String get searchWebButtonLabel => 'Tafuta kwenye Wavuti';

  @override
  String get selectAllButtonLabel => 'Chagua vyote';

  @override
  String get shareButtonLabel => 'Tuma';
}

/// The translations for Tamil (`ta`).
class WidgetsLocalizationTa extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Tamil.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationTa() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'நகலெடு';

  @override
  String get cutButtonLabel => 'வெட்டு';

  @override
  String get lookUpButtonLabel => 'தேடு';

  @override
  String get pasteButtonLabel => 'ஒட்டு';

  @override
  String get reorderItemDown => 'கீழே நகர்த்தவும்';

  @override
  String get reorderItemLeft => 'இடப்புறம் நகர்த்தவும்';

  @override
  String get reorderItemRight => 'வலப்புறம் நகர்த்தவும்';

  @override
  String get reorderItemToEnd => 'இறுதிக்கு நகர்த்தவும்';

  @override
  String get reorderItemToStart => 'தொடக்கத்திற்கு நகர்த்தவும்';

  @override
  String get reorderItemUp => 'மேலே நகர்த்தவும்';

  @override
  String get searchWebButtonLabel => 'இணையத்தில் தேடு';

  @override
  String get selectAllButtonLabel => 'அனைத்தையும் தேர்ந்தெடு';

  @override
  String get shareButtonLabel => 'பகிர்';
}

/// The translations for Telugu (`te`).
class WidgetsLocalizationTe extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Telugu.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationTe() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'కాపీ చేయి';

  @override
  String get cutButtonLabel => 'కత్తిరించండి';

  @override
  String get lookUpButtonLabel => 'వెతకండి';

  @override
  String get pasteButtonLabel => 'పేస్ట్ చేయండి';

  @override
  String get reorderItemDown => 'కిందికు జరుపు';

  @override
  String get reorderItemLeft => 'ఎడమవైపుగా జరపండి';

  @override
  String get reorderItemRight => 'కుడివైపుగా జరపండి';

  @override
  String get reorderItemToEnd => 'చివరకు తరలించండి';

  @override
  String get reorderItemToStart => 'ప్రారంభానికి తరలించండి';

  @override
  String get reorderItemUp => 'పైకి జరపండి';

  @override
  String get searchWebButtonLabel => 'వెబ్‌లో సెర్చ్ చేయండి';

  @override
  String get selectAllButtonLabel => 'అన్నింటినీ ఎంచుకోండి';

  @override
  String get shareButtonLabel => 'షేర్ చేయండి';
}

/// The translations for Thai (`th`).
class WidgetsLocalizationTh extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Thai.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationTh() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'คัดลอก';

  @override
  String get cutButtonLabel => 'ตัด';

  @override
  String get lookUpButtonLabel => 'ค้นหา';

  @override
  String get pasteButtonLabel => 'วาง';

  @override
  String get reorderItemDown => 'ย้ายลง';

  @override
  String get reorderItemLeft => 'ย้ายไปทางซ้าย';

  @override
  String get reorderItemRight => 'ย้ายไปทางขวา';

  @override
  String get reorderItemToEnd => 'ย้ายไปท้ายรายการ';

  @override
  String get reorderItemToStart => 'ย้ายไปต้นรายการ';

  @override
  String get reorderItemUp => 'ย้ายขึ้น';

  @override
  String get searchWebButtonLabel => 'ค้นหาบนอินเทอร์เน็ต';

  @override
  String get selectAllButtonLabel => 'เลือกทั้งหมด';

  @override
  String get shareButtonLabel => 'แชร์';
}

/// The translations for Tagalog (`tl`).
class WidgetsLocalizationTl extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Tagalog.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationTl() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopyahin';

  @override
  String get cutButtonLabel => 'I-cut';

  @override
  String get lookUpButtonLabel => 'Tumingin sa Itaas';

  @override
  String get pasteButtonLabel => 'I-paste';

  @override
  String get reorderItemDown => 'Ilipat pababa';

  @override
  String get reorderItemLeft => 'Ilipat pakaliwa';

  @override
  String get reorderItemRight => 'Ilipat pakanan';

  @override
  String get reorderItemToEnd => 'Ilipat sa dulo';

  @override
  String get reorderItemToStart => 'Ilipat sa simula';

  @override
  String get reorderItemUp => 'Ilipat pataas';

  @override
  String get searchWebButtonLabel => 'Maghanap sa Web';

  @override
  String get selectAllButtonLabel => 'Piliin lahat';

  @override
  String get shareButtonLabel => 'I-share';
}

/// The translations for Turkish (`tr`).
class WidgetsLocalizationTr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Turkish.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationTr() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopyala';

  @override
  String get cutButtonLabel => 'Kes';

  @override
  String get lookUpButtonLabel => 'Ara';

  @override
  String get pasteButtonLabel => 'Yapıştır';

  @override
  String get reorderItemDown => 'Aşağı taşı';

  @override
  String get reorderItemLeft => 'Sola taşı';

  @override
  String get reorderItemRight => 'Sağa taşı';

  @override
  String get reorderItemToEnd => 'Sona taşı';

  @override
  String get reorderItemToStart => 'Başa taşı';

  @override
  String get reorderItemUp => 'Yukarı taşı';

  @override
  String get searchWebButtonLabel => "Web'de Ara";

  @override
  String get selectAllButtonLabel => 'Tümünü seç';

  @override
  String get shareButtonLabel => 'Paylaş';
}

/// The translations for Ukrainian (`uk`).
class WidgetsLocalizationUk extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Ukrainian.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationUk() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Копіювати';

  @override
  String get cutButtonLabel => 'Вирізати';

  @override
  String get lookUpButtonLabel => 'Шукати';

  @override
  String get pasteButtonLabel => 'Вставити';

  @override
  String get reorderItemDown => 'Перемістити вниз';

  @override
  String get reorderItemLeft => 'Перемістити ліворуч';

  @override
  String get reorderItemRight => 'Перемістити праворуч';

  @override
  String get reorderItemToEnd => 'Перемістити в кінець';

  @override
  String get reorderItemToStart => 'Перемістити на початок';

  @override
  String get reorderItemUp => 'Перемістити вгору';

  @override
  String get searchWebButtonLabel => 'Пошук в Інтернеті';

  @override
  String get selectAllButtonLabel => 'Вибрати всі';

  @override
  String get shareButtonLabel => 'Поділитися';
}

/// The translations for Urdu (`ur`).
class WidgetsLocalizationUr extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Urdu.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationUr() : super(TextDirection.rtl);

  @override
  String get copyButtonLabel => 'کاپی کریں';

  @override
  String get cutButtonLabel => 'کٹ کریں';

  @override
  String get lookUpButtonLabel => 'تفصیل دیکھیں';

  @override
  String get pasteButtonLabel => 'پیسٹ کریں';

  @override
  String get reorderItemDown => 'نیچے منتقل کریں';

  @override
  String get reorderItemLeft => 'بائیں منتقل کریں';

  @override
  String get reorderItemRight => 'دائیں منتقل کریں';

  @override
  String get reorderItemToEnd => 'آخر میں منتقل کریں';

  @override
  String get reorderItemToStart => 'شروع میں منتقل کریں';

  @override
  String get reorderItemUp => 'اوپر منتقل کریں';

  @override
  String get searchWebButtonLabel => 'ویب تلاش کریں';

  @override
  String get selectAllButtonLabel => 'سبھی کو منتخب کریں';

  @override
  String get shareButtonLabel => 'اشتراک کریں';
}

/// The translations for Uzbek (`uz`).
class WidgetsLocalizationUz extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Uzbek.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationUz() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Nusxa olish';

  @override
  String get cutButtonLabel => 'Kesib olish';

  @override
  String get lookUpButtonLabel => 'Tepaga qarang';

  @override
  String get pasteButtonLabel => 'Joylash';

  @override
  String get reorderItemDown => 'Pastga siljitish';

  @override
  String get reorderItemLeft => 'Chapga siljitish';

  @override
  String get reorderItemRight => 'Oʻngga siljitish';

  @override
  String get reorderItemToEnd => 'Oxiriga siljitish';

  @override
  String get reorderItemToStart => 'Boshiga siljitish';

  @override
  String get reorderItemUp => 'Tepaga siljitish';

  @override
  String get searchWebButtonLabel => 'Internetdan qidirish';

  @override
  String get selectAllButtonLabel => 'Hammasi';

  @override
  String get shareButtonLabel => 'Ulashish';
}

/// The translations for Vietnamese (`vi`).
class WidgetsLocalizationVi extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Vietnamese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationVi() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Sao chép';

  @override
  String get cutButtonLabel => 'Cắt';

  @override
  String get lookUpButtonLabel => 'Tra cứu';

  @override
  String get pasteButtonLabel => 'Dán';

  @override
  String get reorderItemDown => 'Di chuyển xuống';

  @override
  String get reorderItemLeft => 'Di chuyển sang trái';

  @override
  String get reorderItemRight => 'Di chuyển sang phải';

  @override
  String get reorderItemToEnd => 'Di chuyển xuống cuối danh sách';

  @override
  String get reorderItemToStart => 'Di chuyển lên đầu danh sách';

  @override
  String get reorderItemUp => 'Di chuyển lên';

  @override
  String get searchWebButtonLabel => 'Tìm kiếm trên web';

  @override
  String get selectAllButtonLabel => 'Chọn tất cả';

  @override
  String get shareButtonLabel => 'Chia sẻ';
}

/// The translations for Chinese (`zh`).
class WidgetsLocalizationZh extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Chinese.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZh() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => '复制';

  @override
  String get cutButtonLabel => '剪切';

  @override
  String get lookUpButtonLabel => '查询';

  @override
  String get pasteButtonLabel => '粘贴';

  @override
  String get reorderItemDown => '下移';

  @override
  String get reorderItemLeft => '左移';

  @override
  String get reorderItemRight => '右移';

  @override
  String get reorderItemToEnd => '移到末尾';

  @override
  String get reorderItemToStart => '移到开头';

  @override
  String get reorderItemUp => '上移';

  @override
  String get searchWebButtonLabel => '搜索';

  @override
  String get selectAllButtonLabel => '全选';

  @override
  String get shareButtonLabel => '分享';
}

/// The translations for Chinese, using the Han script (`zh_Hans`).
class WidgetsLocalizationZhHans extends WidgetsLocalizationZh {
  /// Create an instance of the translation bundle for Chinese, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZhHans();
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class WidgetsLocalizationZhHant extends WidgetsLocalizationZh {
  /// Create an instance of the translation bundle for Chinese, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZhHant();

  @override
  String get copyButtonLabel => '複製';

  @override
  String get cutButtonLabel => '剪下';

  @override
  String get lookUpButtonLabel => '查詢';

  @override
  String get pasteButtonLabel => '貼上';

  @override
  String get reorderItemDown => '向下移';

  @override
  String get reorderItemLeft => '向左移';

  @override
  String get reorderItemRight => '向右移';

  @override
  String get reorderItemToEnd => '移到最後';

  @override
  String get reorderItemToStart => '移到開頭';

  @override
  String get reorderItemUp => '向上移';

  @override
  String get searchWebButtonLabel => '搜尋';

  @override
  String get selectAllButtonLabel => '全部選取';
}

/// The translations for Chinese, as used in Hong Kong, using the Han script (`zh_Hant_HK`).
class WidgetsLocalizationZhHantHk extends WidgetsLocalizationZhHant {
  /// Create an instance of the translation bundle for Chinese, as used in Hong Kong, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZhHantHk();
}

/// The translations for Chinese, as used in Taiwan, using the Han script (`zh_Hant_TW`).
class WidgetsLocalizationZhHantTw extends WidgetsLocalizationZhHant {
  /// Create an instance of the translation bundle for Chinese, as used in Taiwan, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZhHantTw();

  @override
  String get reorderItemToStart => '移至開頭';

  @override
  String get reorderItemToEnd => '移至結尾';

  @override
  String get selectAllButtonLabel => '全選';
}

/// The translations for Zulu (`zu`).
class WidgetsLocalizationZu extends GlobalWidgetsLocalizations {
  /// Create an instance of the translation bundle for Zulu.
  ///
  /// For details on the meaning of the arguments, see [GlobalWidgetsLocalizations].
  const WidgetsLocalizationZu() : super(TextDirection.ltr);

  @override
  String get copyButtonLabel => 'Kopisha';

  @override
  String get cutButtonLabel => 'Sika';

  @override
  String get lookUpButtonLabel => 'Bheka Phezulu';

  @override
  String get pasteButtonLabel => 'Namathisela';

  @override
  String get reorderItemDown => 'Iya phansi';

  @override
  String get reorderItemLeft => 'Hambisa kwesokunxele';

  @override
  String get reorderItemRight => 'Yisa kwesokudla';

  @override
  String get reorderItemToEnd => 'Yisa ekugcineni';

  @override
  String get reorderItemToStart => 'Yisa ekuqaleni';

  @override
  String get reorderItemUp => 'Iya phezulu';

  @override
  String get searchWebButtonLabel => 'Sesha Iwebhu';

  @override
  String get selectAllButtonLabel => 'Khetha konke';

  @override
  String get shareButtonLabel => 'Yabelana';
}

/// The set of supported languages, as language code strings.
///
/// The [GlobalWidgetsLocalizations.delegate] can generate localizations for
/// any [Locale] with a language code from this set, regardless of the region.
/// Some regions have specific support (e.g. `de` covers all forms of German,
/// but there is support for `de-CH` specifically to override some of the
/// translations for Switzerland).
///
/// See also:
///
///  * [getWidgetsTranslation], whose documentation describes these values.
final Set<String> kWidgetsSupportedLanguages = HashSet<String>.from(const <String>[
  'af', // Afrikaans
  'am', // Amharic
  'ar', // Arabic
  'as', // Assamese
  'az', // Azerbaijani
  'be', // Belarusian
  'bg', // Bulgarian
  'bn', // Bengali Bangla
  'bs', // Bosnian
  'ca', // Catalan Valencian
  'cs', // Czech
  'cy', // Welsh
  'da', // Danish
  'de', // German
  'el', // Modern Greek
  'en', // English
  'es', // Spanish Castilian
  'et', // Estonian
  'eu', // Basque
  'fa', // Persian
  'fi', // Finnish
  'fil', // Filipino Pilipino
  'fr', // French
  'gl', // Galician
  'gsw', // Swiss German Alemannic Alsatian
  'gu', // Gujarati
  'he', // Hebrew
  'hi', // Hindi
  'hr', // Croatian
  'hu', // Hungarian
  'hy', // Armenian
  'id', // Indonesian
  'is', // Icelandic
  'it', // Italian
  'ja', // Japanese
  'ka', // Georgian
  'kk', // Kazakh
  'km', // Khmer Central Khmer
  'kn', // Kannada
  'ko', // Korean
  'ky', // Kirghiz Kyrgyz
  'lo', // Lao
  'lt', // Lithuanian
  'lv', // Latvian
  'mk', // Macedonian
  'ml', // Malayalam
  'mn', // Mongolian
  'mr', // Marathi
  'ms', // Malay
  'my', // Burmese
  'nb', // Norwegian Bokmål
  'ne', // Nepali
  'nl', // Dutch Flemish
  'no', // Norwegian
  'or', // Oriya
  'pa', // Panjabi Punjabi
  'pl', // Polish
  'ps', // Pushto Pashto
  'pt', // Portuguese
  'ro', // Romanian Moldavian Moldovan
  'ru', // Russian
  'si', // Sinhala Sinhalese
  'sk', // Slovak
  'sl', // Slovenian
  'sq', // Albanian
  'sr', // Serbian
  'sv', // Swedish
  'sw', // Swahili
  'ta', // Tamil
  'te', // Telugu
  'th', // Thai
  'tl', // Tagalog
  'tr', // Turkish
  'uk', // Ukrainian
  'ur', // Urdu
  'uz', // Uzbek
  'vi', // Vietnamese
  'zh', // Chinese
  'zu', // Zulu
]);

/// Creates a [GlobalWidgetsLocalizations] instance for the given `locale`.
///
/// All of the function's arguments except `locale` will be passed to the [
/// GlobalWidgetsLocalizations] constructor. (The `localeName` argument of that
/// constructor is specified by the actual subclass constructor by this
/// function.)
///
/// The following locales are supported by this package:
///
/// {@template flutter.localizations.widgets.languages}
///  * `af` - Afrikaans
///  * `am` - Amharic
///  * `ar` - Arabic
///  * `as` - Assamese
///  * `az` - Azerbaijani
///  * `be` - Belarusian
///  * `bg` - Bulgarian
///  * `bn` - Bengali Bangla
///  * `bs` - Bosnian
///  * `ca` - Catalan Valencian
///  * `cs` - Czech
///  * `cy` - Welsh
///  * `da` - Danish
///  * `de` - German (plus one country variation)
///  * `el` - Modern Greek
///  * `en` - English (plus 8 country variations)
///  * `es` - Spanish Castilian (plus 20 country variations)
///  * `et` - Estonian
///  * `eu` - Basque
///  * `fa` - Persian
///  * `fi` - Finnish
///  * `fil` - Filipino Pilipino
///  * `fr` - French (plus one country variation)
///  * `gl` - Galician
///  * `gsw` - Swiss German Alemannic Alsatian
///  * `gu` - Gujarati
///  * `he` - Hebrew
///  * `hi` - Hindi
///  * `hr` - Croatian
///  * `hu` - Hungarian
///  * `hy` - Armenian
///  * `id` - Indonesian
///  * `is` - Icelandic
///  * `it` - Italian
///  * `ja` - Japanese
///  * `ka` - Georgian
///  * `kk` - Kazakh
///  * `km` - Khmer Central Khmer
///  * `kn` - Kannada
///  * `ko` - Korean
///  * `ky` - Kirghiz Kyrgyz
///  * `lo` - Lao
///  * `lt` - Lithuanian
///  * `lv` - Latvian
///  * `mk` - Macedonian
///  * `ml` - Malayalam
///  * `mn` - Mongolian
///  * `mr` - Marathi
///  * `ms` - Malay
///  * `my` - Burmese
///  * `nb` - Norwegian Bokmål
///  * `ne` - Nepali
///  * `nl` - Dutch Flemish
///  * `no` - Norwegian
///  * `or` - Oriya
///  * `pa` - Panjabi Punjabi
///  * `pl` - Polish
///  * `ps` - Pushto Pashto
///  * `pt` - Portuguese (plus one country variation)
///  * `ro` - Romanian Moldavian Moldovan
///  * `ru` - Russian
///  * `si` - Sinhala Sinhalese
///  * `sk` - Slovak
///  * `sl` - Slovenian
///  * `sq` - Albanian
///  * `sr` - Serbian (plus 2 scripts)
///  * `sv` - Swedish
///  * `sw` - Swahili
///  * `ta` - Tamil
///  * `te` - Telugu
///  * `th` - Thai
///  * `tl` - Tagalog
///  * `tr` - Turkish
///  * `uk` - Ukrainian
///  * `ur` - Urdu
///  * `uz` - Uzbek
///  * `vi` - Vietnamese
///  * `zh` - Chinese (plus 2 country variations and 2 scripts)
///  * `zu` - Zulu
/// {@endtemplate}
///
/// Generally speaking, this method is only intended to be used by
/// [GlobalWidgetsLocalizations.delegate].
GlobalWidgetsLocalizations? getWidgetsTranslation(
  Locale locale,
) {
  switch (locale.languageCode) {
    case 'af':
      return const WidgetsLocalizationAf();
    case 'am':
      return const WidgetsLocalizationAm();
    case 'ar':
      return const WidgetsLocalizationAr();
    case 'as':
      return const WidgetsLocalizationAs();
    case 'az':
      return const WidgetsLocalizationAz();
    case 'be':
      return const WidgetsLocalizationBe();
    case 'bg':
      return const WidgetsLocalizationBg();
    case 'bn':
      return const WidgetsLocalizationBn();
    case 'bs':
      return const WidgetsLocalizationBs();
    case 'ca':
      return const WidgetsLocalizationCa();
    case 'cs':
      return const WidgetsLocalizationCs();
    case 'cy':
      return const WidgetsLocalizationCy();
    case 'da':
      return const WidgetsLocalizationDa();
    case 'de': {
      switch (locale.countryCode) {
        case 'CH':
          return const WidgetsLocalizationDeCh();
      }
      return const WidgetsLocalizationDe();
    }
    case 'el':
      return const WidgetsLocalizationEl();
    case 'en': {
      switch (locale.countryCode) {
        case 'AU':
          return const WidgetsLocalizationEnAu();
        case 'CA':
          return const WidgetsLocalizationEnCa();
        case 'GB':
          return const WidgetsLocalizationEnGb();
        case 'IE':
          return const WidgetsLocalizationEnIe();
        case 'IN':
          return const WidgetsLocalizationEnIn();
        case 'NZ':
          return const WidgetsLocalizationEnNz();
        case 'SG':
          return const WidgetsLocalizationEnSg();
        case 'ZA':
          return const WidgetsLocalizationEnZa();
      }
      return const WidgetsLocalizationEn();
    }
    case 'es': {
      switch (locale.countryCode) {
        case '419':
          return const WidgetsLocalizationEs419();
        case 'AR':
          return const WidgetsLocalizationEsAr();
        case 'BO':
          return const WidgetsLocalizationEsBo();
        case 'CL':
          return const WidgetsLocalizationEsCl();
        case 'CO':
          return const WidgetsLocalizationEsCo();
        case 'CR':
          return const WidgetsLocalizationEsCr();
        case 'DO':
          return const WidgetsLocalizationEsDo();
        case 'EC':
          return const WidgetsLocalizationEsEc();
        case 'GT':
          return const WidgetsLocalizationEsGt();
        case 'HN':
          return const WidgetsLocalizationEsHn();
        case 'MX':
          return const WidgetsLocalizationEsMx();
        case 'NI':
          return const WidgetsLocalizationEsNi();
        case 'PA':
          return const WidgetsLocalizationEsPa();
        case 'PE':
          return const WidgetsLocalizationEsPe();
        case 'PR':
          return const WidgetsLocalizationEsPr();
        case 'PY':
          return const WidgetsLocalizationEsPy();
        case 'SV':
          return const WidgetsLocalizationEsSv();
        case 'US':
          return const WidgetsLocalizationEsUs();
        case 'UY':
          return const WidgetsLocalizationEsUy();
        case 'VE':
          return const WidgetsLocalizationEsVe();
      }
      return const WidgetsLocalizationEs();
    }
    case 'et':
      return const WidgetsLocalizationEt();
    case 'eu':
      return const WidgetsLocalizationEu();
    case 'fa':
      return const WidgetsLocalizationFa();
    case 'fi':
      return const WidgetsLocalizationFi();
    case 'fil':
      return const WidgetsLocalizationFil();
    case 'fr': {
      switch (locale.countryCode) {
        case 'CA':
          return const WidgetsLocalizationFrCa();
      }
      return const WidgetsLocalizationFr();
    }
    case 'gl':
      return const WidgetsLocalizationGl();
    case 'gsw':
      return const WidgetsLocalizationGsw();
    case 'gu':
      return const WidgetsLocalizationGu();
    case 'he':
      return const WidgetsLocalizationHe();
    case 'hi':
      return const WidgetsLocalizationHi();
    case 'hr':
      return const WidgetsLocalizationHr();
    case 'hu':
      return const WidgetsLocalizationHu();
    case 'hy':
      return const WidgetsLocalizationHy();
    case 'id':
      return const WidgetsLocalizationId();
    case 'is':
      return const WidgetsLocalizationIs();
    case 'it':
      return const WidgetsLocalizationIt();
    case 'ja':
      return const WidgetsLocalizationJa();
    case 'ka':
      return const WidgetsLocalizationKa();
    case 'kk':
      return const WidgetsLocalizationKk();
    case 'km':
      return const WidgetsLocalizationKm();
    case 'kn':
      return const WidgetsLocalizationKn();
    case 'ko':
      return const WidgetsLocalizationKo();
    case 'ky':
      return const WidgetsLocalizationKy();
    case 'lo':
      return const WidgetsLocalizationLo();
    case 'lt':
      return const WidgetsLocalizationLt();
    case 'lv':
      return const WidgetsLocalizationLv();
    case 'mk':
      return const WidgetsLocalizationMk();
    case 'ml':
      return const WidgetsLocalizationMl();
    case 'mn':
      return const WidgetsLocalizationMn();
    case 'mr':
      return const WidgetsLocalizationMr();
    case 'ms':
      return const WidgetsLocalizationMs();
    case 'my':
      return const WidgetsLocalizationMy();
    case 'nb':
      return const WidgetsLocalizationNb();
    case 'ne':
      return const WidgetsLocalizationNe();
    case 'nl':
      return const WidgetsLocalizationNl();
    case 'no':
      return const WidgetsLocalizationNo();
    case 'or':
      return const WidgetsLocalizationOr();
    case 'pa':
      return const WidgetsLocalizationPa();
    case 'pl':
      return const WidgetsLocalizationPl();
    case 'ps':
      return const WidgetsLocalizationPs();
    case 'pt': {
      switch (locale.countryCode) {
        case 'PT':
          return const WidgetsLocalizationPtPt();
      }
      return const WidgetsLocalizationPt();
    }
    case 'ro':
      return const WidgetsLocalizationRo();
    case 'ru':
      return const WidgetsLocalizationRu();
    case 'si':
      return const WidgetsLocalizationSi();
    case 'sk':
      return const WidgetsLocalizationSk();
    case 'sl':
      return const WidgetsLocalizationSl();
    case 'sq':
      return const WidgetsLocalizationSq();
    case 'sr': {
      switch (locale.scriptCode) {
        case 'Cyrl': {
          return const WidgetsLocalizationSrCyrl();
        }
        case 'Latn': {
          return const WidgetsLocalizationSrLatn();
        }
      }
      return const WidgetsLocalizationSr();
    }
    case 'sv':
      return const WidgetsLocalizationSv();
    case 'sw':
      return const WidgetsLocalizationSw();
    case 'ta':
      return const WidgetsLocalizationTa();
    case 'te':
      return const WidgetsLocalizationTe();
    case 'th':
      return const WidgetsLocalizationTh();
    case 'tl':
      return const WidgetsLocalizationTl();
    case 'tr':
      return const WidgetsLocalizationTr();
    case 'uk':
      return const WidgetsLocalizationUk();
    case 'ur':
      return const WidgetsLocalizationUr();
    case 'uz':
      return const WidgetsLocalizationUz();
    case 'vi':
      return const WidgetsLocalizationVi();
    case 'zh': {
      switch (locale.scriptCode) {
        case 'Hans': {
          return const WidgetsLocalizationZhHans();
        }
        case 'Hant': {
          switch (locale.countryCode) {
            case 'HK':
              return const WidgetsLocalizationZhHantHk();
            case 'TW':
              return const WidgetsLocalizationZhHantTw();
          }
          return const WidgetsLocalizationZhHant();
        }
      }
      switch (locale.countryCode) {
        case 'HK':
          return const WidgetsLocalizationZhHantHk();
        case 'TW':
          return const WidgetsLocalizationZhHantTw();
      }
      return const WidgetsLocalizationZh();
    }
    case 'zu':
      return const WidgetsLocalizationZu();
  }
  assert(false, 'getWidgetsTranslation() called for unsupported locale "$locale"');
  return null;
}

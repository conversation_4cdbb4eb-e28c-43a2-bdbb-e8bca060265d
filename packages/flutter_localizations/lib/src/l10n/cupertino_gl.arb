{"datePickerHourSemanticsLabelOne": "$hour en punto", "datePickerHourSemanticsLabelOther": "$hour en punto", "datePickerMinuteSemanticsLabelOne": "1 minuto", "datePickerMinuteSemanticsLabelOther": "$minute minutos", "datePickerDateOrder": "dmy", "datePickerDateTimeOrder": "date_time_dayPeriod", "anteMeridiemAbbreviation": "a.m.", "postMeridiemAbbreviation": "p.m.", "todayLabel": "Hoxe", "alertDialogLabel": "<PERSON><PERSON><PERSON>", "timerPickerHourLabelOne": "hora", "timerPickerHourLabelOther": "horas", "timerPickerMinuteLabelOne": "min", "timerPickerMinuteLabelOther": "min", "timerPickerSecondLabelOne": "s", "timerPickerSecondLabelOther": "s", "cutButtonLabel": "Cortar", "copyButtonLabel": "Copiar", "pasteButtonLabel": "<PERSON><PERSON><PERSON>", "selectAllButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "tabSemanticsLabel": "Pestana $tabIndex de $tabCount", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON>", "searchTextFieldPlaceholderLabel": "Fai unha busca", "noSpellCheckReplacementsLabel": "Non se encontrou ningunha substitución", "menuDismissLabel": "<PERSON><PERSON><PERSON>", "lookUpButtonLabel": "Mirar cara arriba", "searchWebButtonLabel": "Buscar na Web", "shareButtonLabel": "Compartir…", "clearButtonLabel": "Bo<PERSON>r", "cancelButtonLabel": "<PERSON><PERSON><PERSON>", "backButtonLabel": "Atrás"}